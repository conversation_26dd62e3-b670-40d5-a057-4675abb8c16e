<?php
// admin/assign_wali.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Aks<PERSON> ditolak. Halaman ini hanya untuk administrator.</div>');
}

$id_kelas = $_GET['kelas'] ?? null;
$id_tahun = $_GET['tahun'] ?? null;
$message = '';

if (!$id_kelas || !$id_tahun) {
    die('<div class="alert alert-danger">Parameter kelas dan tahun harus diisi.</div>');
}

// Ambil data kelas
$stmt = $conn->prepare("SELECT * FROM kelas WHERE id_kelas = ?");
$stmt->bind_param("i", $id_kelas);
$stmt->execute();
$kelas = $stmt->get_result()->fetch_assoc();

if (!$kelas) {
    die('<div class="alert alert-danger">Kelas tidak ditemukan.</div>');
}

// Ambil data tahun
$stmt = $conn->prepare("SELECT * FROM tahun_pelajaran WHERE id_tahun = ?");
$stmt->bind_param("i", $id_tahun);
$stmt->execute();
$tahun = $stmt->get_result()->fetch_assoc();

if (!$tahun) {
    die('<div class="alert alert-danger">Tahun pelajaran tidak ditemukan.</div>');
}

// Handle form submission
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'assign_wali') {
    $id_user = $_POST['id_user'];
    
    if (empty($id_user)) {
        $message = '<div class="alert alert-danger">Pilih wali kelas terlebih dahulu.</div>';
    } else {
        // Cek apakah sudah ada assignment untuk kelas dan tahun ini
        $stmt = $conn->prepare("SELECT id FROM user_kelas WHERE id_kelas = ? AND id_tahun = ? AND id_user = ?");
        $stmt->bind_param("iii", $id_kelas, $id_tahun, $id_user);
        $stmt->execute();
        
        if ($stmt->get_result()->num_rows > 0) {
            $message = '<div class="alert alert-warning">Wali kelas sudah di-assign untuk kelas dan tahun ini.</div>';
        } else {
            // Insert assignment
            $stmt = $conn->prepare("INSERT INTO user_kelas (id_user, id_kelas, id_tahun) VALUES (?, ?, ?)");
            $stmt->bind_param("iii", $id_user, $id_kelas, $id_tahun);
            
            if ($stmt->execute()) {
                $message = '<div class="alert alert-success">Wali kelas berhasil di-assign.</div>';
            } else {
                $message = '<div class="alert alert-danger">Gagal meng-assign wali kelas.</div>';
            }
        }
    }
}

// Ambil daftar wali kelas yang tersedia
$stmt = $conn->prepare("SELECT * FROM users WHERE role = 'wali_kelas' AND is_active = 1 ORDER BY nama_lengkap");
$stmt->execute();
$wali_kelas_list = $stmt->get_result();

// Ambil wali kelas yang sudah di-assign untuk kelas dan tahun ini
$stmt = $conn->prepare("
    SELECT u.id_user, u.nama_lengkap, u.username
    FROM users u
    JOIN user_kelas uk ON u.id_user = uk.id_user
    WHERE uk.id_kelas = ? AND uk.id_tahun = ?
");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$assigned_wali = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="fas fa-user-plus"></i> Assign Wali Kelas</h3>
    <a href="dashboard.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Kembali
    </a>
</div>

<?= $message ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user-plus"></i> Assign Wali Kelas Baru</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="assign_wali">
                    
                    <div class="mb-3">
                        <label class="form-label">Pilih Wali Kelas</label>
                        <select name="id_user" class="form-control" required>
                            <option value="">-- Pilih Wali Kelas --</option>
                            <?php while($wali = $wali_kelas_list->fetch_assoc()): ?>
                                <option value="<?= $wali['id_user'] ?>">
                                    <?= htmlspecialchars($wali['nama_lengkap']) ?> (<?= htmlspecialchars($wali['username']) ?>)
                                </option>
                            <?php endwhile; ?>
                        </select>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Assign Wali Kelas
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Wali Kelas yang Sudah Di-assign -->
        <?php if ($assigned_wali->num_rows > 0): ?>
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="fas fa-users"></i> Wali Kelas yang Sudah Di-assign</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <?php while($wali = $assigned_wali->fetch_assoc()): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?= htmlspecialchars($wali['nama_lengkap']) ?></strong>
                                    <small class="text-muted">(<?= htmlspecialchars($wali['username']) ?>)</small>
                                </div>
                                <div>
                                    <span class="badge bg-success">Assigned</span>
                                    <a href="assign_kelas.php?user=<?= $wali['id_user'] ?>" class="btn btn-sm btn-info ms-2">
                                        <i class="fas fa-cog"></i> Atur Kelas
                                    </a>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Info Assignment</h5>
            </div>
            <div class="card-body">
                <p><strong>Kelas:</strong> <?= htmlspecialchars($kelas['nama_kelas']) ?></p>
                <p><strong>Tingkat:</strong> <?= htmlspecialchars($kelas['tingkat'] ?? '-') ?></p>
                <p><strong>Tahun Pelajaran:</strong> <?= htmlspecialchars($tahun['tahun_ajaran']) ?></p>
                <p><strong>Status Tahun:</strong> 
                    <span class="badge <?= $tahun['is_active'] ? 'bg-success' : 'bg-secondary' ?>">
                        <?= $tahun['is_active'] ? 'Aktif' : 'Tidak Aktif' ?>
                    </span>
                </p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-lightbulb"></i> Catatan</h5>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li>Satu kelas dapat memiliki beberapa wali kelas</li>
                    <li>Satu wali kelas dapat mengajar beberapa kelas</li>
                    <li>Assignment berlaku per tahun pelajaran</li>
                    <li>Wali kelas hanya dapat melihat data siswa dari kelas yang di-assign</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
