-- fix_gambaran_kelas.sql
-- Script untuk menambah kolom gambaran kelas jika belum ada

-- Cek dan tambah kolom hasil_komitmen_bersama
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'kelas' 
  AND column_name = 'hasil_komitmen_bersama';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE kelas ADD COLUMN hasil_komitmen_bersama TEXT AFTER tingkat',
    'SELECT ''Kolom hasil_komitmen_bersama sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom aspek_sanitas
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'kelas' 
  AND column_name = 'aspek_sanitas';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE kelas ADD COLUMN aspek_sanitas TEXT AFTER hasil_komitmen_bersama',
    'SELECT ''Kolom aspek_sanitas sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom aspek_sactitas
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'kelas' 
  AND column_name = 'aspek_sactitas';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE kelas ADD COLUMN aspek_sactitas TEXT AFTER aspek_sanitas',
    'SELECT ''Kolom aspek_sactitas sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom aspek_scientia
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'kelas' 
  AND column_name = 'aspek_scientia';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE kelas ADD COLUMN aspek_scientia TEXT AFTER aspek_sactitas',
    'SELECT ''Kolom aspek_scientia sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_agama
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'kelas' 
  AND column_name = 'mapel_agama';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE kelas ADD COLUMN mapel_agama TEXT AFTER aspek_scientia',
    'SELECT ''Kolom mapel_agama sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_bahasa_indonesia
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'kelas' 
  AND column_name = 'mapel_bahasa_indonesia';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE kelas ADD COLUMN mapel_bahasa_indonesia TEXT AFTER mapel_agama',
    'SELECT ''Kolom mapel_bahasa_indonesia sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_bahasa_inggris
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'kelas' 
  AND column_name = 'mapel_bahasa_inggris';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE kelas ADD COLUMN mapel_bahasa_inggris TEXT AFTER mapel_bahasa_indonesia',
    'SELECT ''Kolom mapel_bahasa_inggris sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_bahasa_latin
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'kelas' 
  AND column_name = 'mapel_bahasa_latin';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE kelas ADD COLUMN mapel_bahasa_latin TEXT AFTER mapel_bahasa_inggris',
    'SELECT ''Kolom mapel_bahasa_latin sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_lain
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'kelas' 
  AND column_name = 'mapel_lain';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE kelas ADD COLUMN mapel_lain TEXT AFTER mapel_bahasa_latin',
    'SELECT ''Kolom mapel_lain sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing records dengan nilai default kosong
UPDATE kelas SET 
    hasil_komitmen_bersama = COALESCE(hasil_komitmen_bersama, ''),
    aspek_sanitas = COALESCE(aspek_sanitas, ''),
    aspek_sactitas = COALESCE(aspek_sactitas, ''),
    aspek_scientia = COALESCE(aspek_scientia, ''),
    mapel_agama = COALESCE(mapel_agama, ''),
    mapel_bahasa_indonesia = COALESCE(mapel_bahasa_indonesia, ''),
    mapel_bahasa_inggris = COALESCE(mapel_bahasa_inggris, ''),
    mapel_bahasa_latin = COALESCE(mapel_bahasa_latin, ''),
    mapel_lain = COALESCE(mapel_lain, '')
WHERE hasil_komitmen_bersama IS NULL 
   OR aspek_sanitas IS NULL 
   OR aspek_sactitas IS NULL 
   OR aspek_scientia IS NULL 
   OR mapel_agama IS NULL 
   OR mapel_bahasa_indonesia IS NULL 
   OR mapel_bahasa_inggris IS NULL 
   OR mapel_bahasa_latin IS NULL 
   OR mapel_lain IS NULL;

-- Tampilkan hasil
SELECT 
    'Kolom gambaran kelas berhasil ditambahkan/diupdate' as status,
    COUNT(*) as total_kelas
FROM kelas;

-- Tampilkan struktur tabel kelas
DESCRIBE kelas;
