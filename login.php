<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
include 'includes/db.php';
include 'includes/functions.php';

$error = '';

if ($_POST) {
    $username = trim($_POST['username']);
    $password = $_POST['password'];

    // Validasi input
    if (empty($username) || empty($password)) {
        $error = "Username dan password harus diisi.";
    } else {
        // Autentikasi user
        $user = authenticateUser($conn, $username, $password);

        if ($user) {
            // Ambil kelas yang dapat diakses user
            $tahun_aktif = getActiveTahun($conn);
            $kelas_list = getUserKelas($conn, $user['id_user'], $tahun_aktif['id_tahun']);

            // Set session
            setUserSession($user, $kelas_list);

            // Redirect berdasarkan role
            if ($user['role'] === 'admin') {
                header("Location: admin/dashboard.php");
            } else {
                // Redirect ke kelas pertama yang dapat diakses
                if (!empty($kelas_list)) {
                    $first_kelas = $kelas_list[0];
                    header("Location: guru/gambaran_kelas.php?kelas=" . $first_kelas['id_kelas'] . "&tahun=" . $first_kelas['id_tahun']);
                } else {
                    $error = "Anda belum memiliki akses ke kelas manapun. Hubungi administrator.";
                }
            }
            exit;
        } else {
            $error = "Username atau password salah.";
        }
    }
}
?>
<!-- Form login HTML -->
<!DOCTYPE html>
<html>
<head>
    <title>Login Cura Personalis</title>
    <link href="assets/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
<div class="container mt-5" style="max-width: 400px;">
    <div class="card">
        <div class="card-body">
            <h4 class="text-center">Login Cura Personalis</h4>
            <?php if (!empty($error)) echo "<div class='alert alert-danger'>$error</div>"; ?>
            <form method="POST">
                <div class="mb-3">
                    <label>Username</label>
                    <input type="text" name="username" class="form-control" required>
                </div>
                <div class="mb-3">
                    <label>Password</label>
                    <input type="password" name="password" class="form-control" required>
                </div>
                <button type="submit" class="btn btn-primary w-100">Login</button>
            </form>
        </div>
    </div>
</div>
</body>
</html>