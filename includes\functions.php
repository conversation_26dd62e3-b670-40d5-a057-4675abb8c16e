<?php
function redirect($url) {
    header("Location: $url");
    exit;
}

function isLoggedIn() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

function uploadFoto($file, $id_siswa) {
    $target_dir = "uploads/foto/";
    $imageFileType = strtolower(pathinfo($file["name"], PATHINFO_EXTENSION));
    $filename = "siswa_" . $id_siswa . "." . $imageFileType;
    $target_file = $target_dir . $filename;

    if (move_uploaded_file($file["tmp_name"], $target_file)) {
        return $filename;
    }
    return "placeholder.jpg";
}

/**
 * Autentikasi user berdasarkan username dan password
 */
function authenticateUser($conn, $username, $password) {
    $stmt = $conn->prepare("SELECT id_user, username, password, nama_lengkap, email, role, is_active FROM users WHERE username = ? AND is_active = 1");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($user = $result->fetch_assoc()) {
        if (password_verify($password, $user['password'])) {
            return $user;
        }
    }
    return false;
}

/**
 * Mendapatkan kelas yang dapat diakses oleh user
 */
function getUserKelas($conn, $id_user, $id_tahun = null) {
    if ($id_tahun === null) {
        // Ambil tahun aktif
        $stmt = $conn->prepare("SELECT id_tahun FROM tahun_pelajaran WHERE is_active = 1 LIMIT 1");
        $stmt->execute();
        $tahun = $stmt->get_result()->fetch_assoc();
        $id_tahun = $tahun['id_tahun'] ?? 1;
    }

    $stmt = $conn->prepare("
        SELECT uk.id_kelas, k.nama_kelas, k.tingkat, uk.id_tahun, tp.tahun_ajaran
        FROM user_kelas uk
        JOIN kelas k ON uk.id_kelas = k.id_kelas
        JOIN tahun_pelajaran tp ON uk.id_tahun = tp.id_tahun
        WHERE uk.id_user = ? AND uk.id_tahun = ?
        ORDER BY k.nama_kelas
    ");
    $stmt->bind_param("ii", $id_user, $id_tahun);
    $stmt->execute();
    return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
}

/**
 * Cek apakah user dapat mengakses kelas tertentu
 */
function canAccessKelas($conn, $id_user, $id_kelas, $id_tahun = null) {
    // Pastikan session sudah dimulai
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }

    // Admin dapat mengakses semua kelas
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        return true;
    }

    if ($id_tahun === null) {
        $stmt = $conn->prepare("SELECT id_tahun FROM tahun_pelajaran WHERE is_active = 1 LIMIT 1");
        $stmt->execute();
        $tahun = $stmt->get_result()->fetch_assoc();
        $id_tahun = $tahun['id_tahun'] ?? 1;
    }

    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM user_kelas WHERE id_user = ? AND id_kelas = ? AND id_tahun = ?");
    $stmt->bind_param("iii", $id_user, $id_kelas, $id_tahun);
    $stmt->execute();
    $result = $stmt->get_result()->fetch_assoc();

    return $result['count'] > 0;
}

/**
 * Mendapatkan tahun pelajaran aktif
 */
function getActiveTahun($conn) {
    $stmt = $conn->prepare("SELECT id_tahun, tahun_ajaran FROM tahun_pelajaran WHERE is_active = 1 LIMIT 1");
    $stmt->execute();
    return $stmt->get_result()->fetch_assoc();
}

/**
 * Mendapatkan semua tahun pelajaran
 */
function getAllTahun($conn) {
    $stmt = $conn->prepare("SELECT id_tahun, tahun_ajaran, is_active FROM tahun_pelajaran ORDER BY tahun_ajaran DESC");
    $stmt->execute();
    return $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
}

/**
 * Set session data setelah login berhasil
 */
function setUserSession($user, $kelas_list) {
    $_SESSION['logged_in'] = true;
    $_SESSION['id_user'] = $user['id_user'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['nama_lengkap'] = $user['nama_lengkap'];
    $_SESSION['email'] = $user['email'];
    $_SESSION['role'] = $user['role'];
    $_SESSION['kelas_list'] = $kelas_list;

    // Set kelas default (kelas pertama yang dapat diakses)
    if (!empty($kelas_list)) {
        $_SESSION['current_kelas'] = $kelas_list[0]['id_kelas'];
        $_SESSION['current_tahun'] = $kelas_list[0]['id_tahun'];
    }
}

/**
 * Clear session data saat logout
 */
function clearUserSession() {
    if (session_status() == PHP_SESSION_NONE) {
        session_start();
    }
    session_unset();
    session_destroy();
}

/**
 * Middleware untuk memvalidasi akses halaman
 */
function requireLogin() {
    if (!isLoggedIn()) {
        redirect('../login.php');
    }
}

/**
 * Middleware untuk memvalidasi akses kelas
 */
function requireKelasAccess($conn, $id_kelas, $id_tahun = null) {
    requireLogin();

    if (!canAccessKelas($conn, $_SESSION['id_user'], $id_kelas, $id_tahun)) {
        die('<div class="alert alert-danger">Anda tidak memiliki akses ke kelas ini.</div>');
    }
}
?>