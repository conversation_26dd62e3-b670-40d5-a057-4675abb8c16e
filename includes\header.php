<?php
// includes/header.php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
include 'db.php';
include 'functions.php';

// Cek login
requireLogin();

// Ambil informasi user dan kelas
$user_kelas = $_SESSION['kelas_list'] ?? [];
$current_kelas = $_GET['kelas'] ?? $_SESSION['current_kelas'] ?? null;
$current_tahun = $_GET['tahun'] ?? $_SESSION['current_tahun'] ?? null;

// Update current kelas di session jika ada parameter
if (isset($_GET['kelas']) && isset($_GET['tahun'])) {
    $_SESSION['current_kelas'] = $_GET['kelas'];
    $_SESSION['current_tahun'] = $_GET['tahun'];
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cura Personalis</title>
    <!-- Bootstrap 5 CSS -->
    <link href="../assets/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Modern Light Theme -->
    <link href="../assets/css/modern-light-theme.css" rel="stylesheet">
    <!-- Enhanced Components -->
    <link href="../assets/css/enhanced-components.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/custom.css" rel="stylesheet">
</head>
<body>
    <!-- Modern Navbar -->
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container">
            <a class="navbar-brand text-gradient fw-bold fs-4" href="#">
                <i class="fas fa-graduation-cap me-2"></i>Cura Personalis
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php if ($_SESSION['role'] === 'admin'): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="../admin/dashboard.php">Dashboard Admin</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="../admin/manage_users.php">Kelola User</a>
                        </li>
                    <?php endif; ?>

                    <?php if (!empty($user_kelas)): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="kelasDropdown" data-bs-toggle="dropdown">
                                Pilih Kelas
                            </a>
                            <ul class="dropdown-menu">
                                <?php foreach ($user_kelas as $kelas): ?>
                                    <li>
                                        <a class="dropdown-item <?= ($kelas['id_kelas'] == $current_kelas) ? 'active' : '' ?>"
                                           href="gambaran_kelas.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $kelas['id_tahun'] ?>">
                                            <?= htmlspecialchars($kelas['nama_kelas']) ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </li>

                        <?php if ($current_kelas): ?>
                            <li class="nav-item">
                                <a class="nav-link" href="gambaran_kelas.php?kelas=<?= $current_kelas ?>&tahun=<?= $current_tahun ?>">Gambaran Umum</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="data_siswa.php?kelas=<?= $current_kelas ?>&tahun=<?= $current_tahun ?>">Data Siswa</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="potensial.php?kelas=<?= $current_kelas ?>&tahun=<?= $current_tahun ?>">Anak Potensial</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="catatan_khusus.php?kelas=<?= $current_kelas ?>&tahun=<?= $current_tahun ?>">Catatan Khusus</a>
                            </li>
                        <?php endif; ?>
                    <?php endif; ?>
                </ul>

                <!-- User Info -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> <?= htmlspecialchars($_SESSION['nama_lengkap']) ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">Role: <?= ucfirst($_SESSION['role']) ?></h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content Container -->
    <div class="container main-content fade-in-up">