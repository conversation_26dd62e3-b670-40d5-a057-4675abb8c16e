<?php
// test_edit_siswa.php - Test untuk memastikan edit siswa berfungsi

echo "<h2>Test Edit Siswa</h2>";

// Test koneksi database
include 'includes/db.php';

if ($conn->connect_error) {
    echo "<div style='color: red;'>❌ Koneksi database GAGAL: " . $conn->connect_error . "</div>";
    exit;
} else {
    echo "<div style='color: green;'>✅ Koneksi database BERHASIL</div>";
}

// Test query siswa
$result = $conn->query("SELECT COUNT(*) as count FROM siswa");
if ($result) {
    $row = $result->fetch_assoc();
    echo "<div style='color: blue;'>📊 Total siswa: " . $row['count'] . "</div>";
} else {
    echo "<div style='color: red;'>❌ Error query siswa: " . $conn->error . "</div>";
}

// Test struktur tabel siswa
echo "<h3>Struktur Tabel Siswa:</h3>";
$result = $conn->query("DESCRIBE siswa");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<div style='color: red;'>❌ Error describe siswa: " . $conn->error . "</div>";
}

// Test sample data
echo "<h3>Sample Data Siswa:</h3>";
$result = $conn->query("SELECT id_siswa, nama_lengkap, nama_panggilan, status_ppdb FROM siswa LIMIT 3");
if ($result && $result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Nama Lengkap</th><th>Nama Panggilan</th><th>Status PPDB</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id_siswa'] . "</td>";
        echo "<td>" . $row['nama_lengkap'] . "</td>";
        echo "<td>" . $row['nama_panggilan'] . "</td>";
        echo "<td>" . $row['status_ppdb'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<div style='color: orange;'>⚠️ Belum ada data siswa</div>";
}

echo "<br><br>";
echo "<a href='login.php'>🔗 Ke Halaman Login</a> | ";
echo "<a href='guru/data_siswa.php?kelas=1&tahun=1'>🔗 Test Data Siswa</a>";
?>
