<?php
// admin/manage_tahun.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Aks<PERSON> ditolak. Halaman ini hanya untuk administrator.</div>');
}

$message = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_tahun':
                $tahun_ajaran = trim($_POST['tahun_ajaran']);
                
                if (empty($tahun_ajaran)) {
                    $message = '<div class="alert alert-danger">Tahun ajaran harus diisi.</div>';
                } else {
                    // Cek tahun sudah ada
                    $stmt = $conn->prepare("SELECT id_tahun FROM tahun_pelajaran WHERE tahun_ajaran = ?");
                    $stmt->bind_param("s", $tahun_ajaran);
                    $stmt->execute();
                    
                    if ($stmt->get_result()->num_rows > 0) {
                        $message = '<div class="alert alert-danger">Tahun ajaran sudah ada.</div>';
                    } else {
                        // Insert tahun
                        $stmt = $conn->prepare("INSERT INTO tahun_pelajaran (tahun_ajaran) VALUES (?)");
                        $stmt->bind_param("s", $tahun_ajaran);
                        
                        if ($stmt->execute()) {
                            $message = '<div class="alert alert-success">Tahun pelajaran berhasil ditambahkan.</div>';
                        } else {
                            $message = '<div class="alert alert-danger">Gagal menambahkan tahun pelajaran.</div>';
                        }
                    }
                }
                break;
                
            case 'set_active':
                $id_tahun = $_POST['id_tahun'];
                
                // Set semua tahun menjadi tidak aktif
                $conn->query("UPDATE tahun_pelajaran SET is_active = 0");
                
                // Set tahun yang dipilih menjadi aktif
                $stmt = $conn->prepare("UPDATE tahun_pelajaran SET is_active = 1 WHERE id_tahun = ?");
                $stmt->bind_param("i", $id_tahun);
                
                if ($stmt->execute()) {
                    $message = '<div class="alert alert-success">Tahun pelajaran aktif berhasil diubah.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Gagal mengubah tahun pelajaran aktif.</div>';
                }
                break;
        }
    }
}

// Ambil data tahun pelajaran
$stmt = $conn->prepare("
    SELECT tp.*, 
           COUNT(DISTINCT s.id_siswa) as total_siswa,
           COUNT(DISTINCT uk.id_user) as total_wali
    FROM tahun_pelajaran tp
    LEFT JOIN siswa s ON tp.id_tahun = s.id_tahun
    LEFT JOIN user_kelas uk ON tp.id_tahun = uk.id_tahun
    GROUP BY tp.id_tahun
    ORDER BY tp.tahun_ajaran DESC
");
$stmt->execute();
$tahun_list = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="fas fa-calendar"></i> Kelola Tahun Pelajaran</h3>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTahunModal">
            <i class="fas fa-plus"></i> Tambah Tahun
        </button>
        <a href="dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>

<?= $message ?>

<!-- Tabel Tahun Pelajaran -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list"></i> Daftar Tahun Pelajaran</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Tahun Ajaran</th>
                        <th>Status</th>
                        <th>Total Siswa</th>
                        <th>Total Wali Kelas</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while($tahun = $tahun_list->fetch_assoc()): ?>
                        <tr>
                            <td>
                                <strong><?= htmlspecialchars($tahun['tahun_ajaran']) ?></strong>
                                <?php if ($tahun['is_active']): ?>
                                    <span class="badge bg-success ms-2">Aktif</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($tahun['is_active']): ?>
                                    <span class="badge bg-success">Tahun Aktif</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Tidak Aktif</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info"><?= $tahun['total_siswa'] ?> siswa</span>
                            </td>
                            <td>
                                <span class="badge bg-primary"><?= $tahun['total_wali'] ?> wali kelas</span>
                            </td>
                            <td>
                                <?php if (!$tahun['is_active']): ?>
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="set_active">
                                        <input type="hidden" name="id_tahun" value="<?= $tahun['id_tahun'] ?>">
                                        <button type="submit" class="btn btn-sm btn-success"
                                                onclick="return confirm('Yakin ingin mengaktifkan tahun pelajaran ini?')">
                                            <i class="fas fa-check"></i> Aktifkan
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <span class="text-muted">Tahun Aktif</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Info Card -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-info-circle"></i> Informasi Penting</h5>
            </div>
            <div class="card-body">
                <ul>
                    <li><strong>Tahun Aktif:</strong> Hanya satu tahun pelajaran yang dapat aktif pada satu waktu</li>
                    <li><strong>Data Siswa:</strong> Setiap siswa terkait dengan tahun pelajaran tertentu</li>
                    <li><strong>Assignment Wali:</strong> Wali kelas di-assign per tahun pelajaran</li>
                    <li><strong>Perubahan Tahun Aktif:</strong> Akan mempengaruhi tampilan data di seluruh sistem</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Modal Tambah Tahun -->
<div class="modal fade" id="addTahunModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Tahun Pelajaran</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_tahun">
                    
                    <div class="mb-3">
                        <label class="form-label">Tahun Ajaran *</label>
                        <input type="text" name="tahun_ajaran" class="form-control" 
                               placeholder="Contoh: 2025/2026" required>
                        <div class="form-text">Format: YYYY/YYYY (contoh: 2025/2026)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Tambah Tahun</button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
