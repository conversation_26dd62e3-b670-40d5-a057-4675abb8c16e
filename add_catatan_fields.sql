-- <PERSON><PERSON>t untuk menambahkan kolom Catatan Lain dan Catatan Kepamongan
-- Jalankan script ini di phpMyAdmin atau MySQL

-- <PERSON>bah kolom catatan_lain setelah kolom catatan
ALTER TABLE siswa ADD COLUMN catatan_lain TEXT AFTER catatan;

-- <PERSON>bah kolom catatan_kepamongan setelah kolom catatan_lain  
ALTER TABLE siswa ADD COLUMN catatan_kepamongan TEXT AFTER catatan_lain;

-- <PERSON><PERSON> hasil
SELECT 'Kolom catatan_lain dan catatan_kepamongan berhasil ditambahkan!' as status;

-- <PERSON><PERSON><PERSON><PERSON> struktur tabel siswa untuk verifikasi
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'siswa' 
  AND TABLE_SCHEMA = DATABASE()
  AND COLUMN_NAME IN ('catatan', 'catatan_lain', 'catatan_kepamongan')
ORDER BY ORDINAL_POSITION;
