-- Database Schema untuk Cura Personalis
-- Jalankan script ini untuk membuat tabel-tabel yang diperlukan

-- Tabel tahun_pelajaran
CREATE TABLE IF NOT EXISTS tahun_pelajaran (
    id_tahun INT PRIMARY KEY AUTO_INCREMENT,
    tahun_ajaran VARCHAR(20) NOT NULL,
    is_active TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel kelas
CREATE TABLE IF NOT EXISTS kelas (
    id_kelas INT PRIMARY KEY AUTO_INCREMENT,
    nama_kelas VARCHAR(50) NOT NULL,
    tingkat VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabel users untuk wali kelas dan admin
CREATE TABLE IF NOT EXISTS users (
    id_user INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    nama_lengkap VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'wali_kelas') DEFAULT 'wali_kelas',
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel relasi user dengan kelas (satu user bisa mengajar beberapa kelas)
CREATE TABLE IF NOT EXISTS user_kelas (
    id INT PRIMARY KEY AUTO_INCREMENT,
    id_user INT NOT NULL,
    id_kelas INT NOT NULL,
    id_tahun INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_user) REFERENCES users(id_user) ON DELETE CASCADE,
    FOREIGN KEY (id_kelas) REFERENCES kelas(id_kelas) ON DELETE CASCADE,
    FOREIGN KEY (id_tahun) REFERENCES tahun_pelajaran(id_tahun) ON DELETE CASCADE,
    UNIQUE KEY unique_user_kelas_tahun (id_user, id_kelas, id_tahun)
);

-- Tabel siswa
CREATE TABLE IF NOT EXISTS siswa (
    id_siswa INT PRIMARY KEY AUTO_INCREMENT,
    nama_lengkap VARCHAR(100) NOT NULL,
    nama_panggilan VARCHAR(50),
    asal_paroki VARCHAR(100),
    keuskupan VARCHAR(100),
    status_ppdb VARCHAR(50),
    gambaran_umum TEXT,
    kondisi_saat_ini TEXT,
    catatan TEXT,
    pendampingan TEXT,
    hasil_pendampingan TEXT,
    foto VARCHAR(255) DEFAULT 'placeholder.jpg',
    id_kelas INT NOT NULL,
    id_tahun INT NOT NULL,
    is_potensial TINYINT(1) DEFAULT 0,
    is_catatan_khusus TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_kelas) REFERENCES kelas(id_kelas),
    FOREIGN KEY (id_tahun) REFERENCES tahun_pelajaran(id_tahun)
);

-- Tabel kelas_gambaran_umum
CREATE TABLE IF NOT EXISTS kelas_gambaran_umum (
    id INT PRIMARY KEY AUTO_INCREMENT,
    id_kelas INT NOT NULL,
    id_tahun INT NOT NULL,
    komitmen_bersama TEXT,
    rata_rata_nilai DECIMAL(5,2),
    nilai_tertinggi DECIMAL(5,2),
    nilai_terendah DECIMAL(5,2),
    rangking_5_terbawah JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_kelas) REFERENCES kelas(id_kelas),
    FOREIGN KEY (id_tahun) REFERENCES tahun_pelajaran(id_tahun),
    UNIQUE KEY unique_kelas_tahun (id_kelas, id_tahun)
);

-- Insert data awal tahun pelajaran
INSERT INTO tahun_pelajaran (tahun_ajaran, is_active) VALUES
('2023/2024', 0),
('2024/2025', 1);

-- Insert data awal kelas
INSERT INTO kelas (nama_kelas, tingkat) VALUES
('KPP A', 'Kelas Persiapan Pastoral'),
('KPP B', 'Kelas Persiapan Pastoral'),
('X-1', 'Kelas 10'),
('X-2', 'Kelas 10'),
('XI-1', 'Kelas 11'),
('XI-2', 'Kelas 11'),
('XII-1', 'Kelas 12'),
('XII-2', 'Kelas 12');

-- Insert user admin default
INSERT INTO users (username, password, nama_lengkap, email, role) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>', 'admin');
-- Password default: password

-- Insert contoh wali kelas
INSERT INTO users (username, password, nama_lengkap, email, role) VALUES
('wali_kppa', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Romo Wali KPP A', '<EMAIL>', 'wali_kelas'),
('wali_kppb', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Romo Wali KPP B', '<EMAIL>', 'wali_kelas'),
('wali_x1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Romo Wali X-1', '<EMAIL>', 'wali_kelas');

-- Assign wali kelas ke kelas mereka untuk tahun aktif
INSERT INTO user_kelas (id_user, id_kelas, id_tahun) VALUES
(2, 1, 2), -- wali_kppa -> KPP A
(3, 2, 2), -- wali_kppb -> KPP B
(4, 3, 2); -- wali_x1 -> X-1

-- Insert contoh data siswa
INSERT INTO siswa (nama_lengkap, nama_panggilan, asal_paroki, keuskupan, status_ppdb, id_kelas, id_tahun) VALUES
('Yohanes Paulus Simbolon', 'Paulus', 'Paroki Kristus Raja', 'Keuskupan Medan', 'Reguler', 1, 2),
('Petrus Kanisius Hutabarat', 'Petrus', 'Paroki Santo Yosef', 'Keuskupan Sibolga', 'Beasiswa', 1, 2),
('Fransiskus Xaverius Manalu', 'Frans', 'Paroki Santa Maria', 'Keuskupan Padang', 'Reguler', 2, 2);