<?php
// admin/manage_siswa_kelas.php - Admin mengelola siswa per kelas

include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Aks<PERSON> ditolak. Halaman ini hanya untuk administrator.</div>');
}

$id_kelas = $_GET['kelas'] ?? 0;
$id_tahun = $_GET['tahun'] ?? 0;

if (!$id_kelas || !$id_tahun) {
    die('<div class="alert alert-danger">Parameter kelas dan tahun harus diisi.</div>');
}

// Get info kelas
$stmt = $conn->prepare("SELECT * FROM kelas WHERE id_kelas = ?");
$stmt->bind_param("i", $id_kelas);
$stmt->execute();
$kelas = $stmt->get_result()->fetch_assoc();

if (!$kelas) {
    die('<div class="alert alert-danger">Kelas tidak ditemukan.</div>');
}

// Get info tahun
$stmt = $conn->prepare("SELECT * FROM tahun_pelajaran WHERE id_tahun = ?");
$stmt->bind_param("i", $id_tahun);
$stmt->execute();
$tahun = $stmt->get_result()->fetch_assoc();

// Get siswa di kelas ini
$stmt = $conn->prepare("
    SELECT s.*, 
           CASE WHEN s.foto IS NULL OR s.foto = '' THEN 'placeholder.jpg' ELSE s.foto END as foto_display
    FROM siswa s 
    WHERE s.id_kelas = ? AND s.id_tahun = ?
    ORDER BY s.nama_lengkap
");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$siswa_list = $stmt->get_result();

// Get wali kelas
$stmt = $conn->prepare("
    SELECT u.nama_lengkap 
    FROM user_kelas uk 
    JOIN users u ON uk.id_user = u.id_user 
    WHERE uk.id_kelas = ? AND uk.id_tahun = ?
");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$wali_result = $stmt->get_result();
$wali_kelas = $wali_result->fetch_assoc();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h3><i class="fas fa-users"></i> Kelola Siswa - <?= htmlspecialchars($kelas['nama_kelas']) ?></h3>
        <p class="text-muted mb-0">
            Tahun: <?= htmlspecialchars($tahun['tahun_ajaran']) ?> | 
            Wali Kelas: <?= $wali_kelas ? htmlspecialchars($wali_kelas['nama_lengkap']) : '<em>Belum ditentukan</em>' ?>
        </p>
    </div>
    <div>
        <a href="view_all_kelas.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
        <a href="add_siswa.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Siswa
        </a>
    </div>
</div>

<!-- Statistik Ringkas -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h4><?= $siswa_list->num_rows ?></h4>
                <p class="mb-0">Total Siswa</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body text-center">
                <?php
                $siswa_list->data_seek(0);
                $potensial = 0;
                while ($s = $siswa_list->fetch_assoc()) {
                    if ($s['is_potensial']) $potensial++;
                }
                $siswa_list->data_seek(0);
                ?>
                <h4><?= $potensial ?></h4>
                <p class="mb-0">Siswa Potensial</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body text-center">
                <?php
                $catatan_khusus = 0;
                while ($s = $siswa_list->fetch_assoc()) {
                    if ($s['is_catatan_khusus']) $catatan_khusus++;
                }
                $siswa_list->data_seek(0);
                ?>
                <h4><?= $catatan_khusus ?></h4>
                <p class="mb-0">Catatan Khusus</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body text-center">
                <?php
                $ada_pendampingan = 0;
                while ($s = $siswa_list->fetch_assoc()) {
                    if (!empty($s['pendampingan'])) $ada_pendampingan++;
                }
                $siswa_list->data_seek(0);
                ?>
                <h4><?= $ada_pendampingan ?></h4>
                <p class="mb-0">Ada Pendampingan</p>
            </div>
        </div>
    </div>
</div>

<!-- Daftar Siswa -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list"></i> Daftar Siswa
        </h5>
    </div>
    <div class="card-body">
        <?php if ($siswa_list->num_rows > 0): ?>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="60">Foto</th>
                        <th>Nama Lengkap</th>
                        <th>Nama Panggilan</th>
                        <th>Asal Paroki</th>
                        <th>Status</th>
                        <th width="150">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while ($siswa = $siswa_list->fetch_assoc()): ?>
                    <tr>
                        <td>
                            <img src="../uploads/<?= htmlspecialchars($siswa['foto_display']) ?>" 
                                 alt="Foto <?= htmlspecialchars($siswa['nama_lengkap']) ?>"
                                 class="rounded-circle" width="40" height="40"
                                 onerror="this.src='../uploads/placeholder.jpg'">
                        </td>
                        <td>
                            <strong><?= htmlspecialchars($siswa['nama_lengkap']) ?></strong>
                            <div class="small text-muted">
                                <?php if ($siswa['is_potensial']): ?>
                                    <span class="badge bg-success">Potensial</span>
                                <?php endif; ?>
                                <?php if ($siswa['is_catatan_khusus']): ?>
                                    <span class="badge bg-warning">Catatan Khusus</span>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td><?= htmlspecialchars($siswa['nama_panggilan']) ?></td>
                        <td>
                            <?= htmlspecialchars($siswa['asal_paroki']) ?>
                            <?php if ($siswa['keuskupan']): ?>
                                <div class="small text-muted"><?= htmlspecialchars($siswa['keuskupan']) ?></div>
                            <?php endif; ?>
                        </td>
                        <td>
                            <span class="badge bg-<?= $siswa['status_ppdb'] == 'Beasiswa' ? 'success' : 'primary' ?>">
                                <?= htmlspecialchars($siswa['status_ppdb']) ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="../guru/detail_siswa.php?id=<?= $siswa['id_siswa'] ?>&kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" 
                                   class="btn btn-outline-primary" title="Lihat Detail">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="edit_siswa.php?id=<?= $siswa['id_siswa'] ?>&kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>"
                                   class="btn btn-outline-warning" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn btn-outline-danger" title="Hapus"
                                        onclick="confirmDelete(<?= $siswa['id_siswa'] ?>, '<?= htmlspecialchars($siswa['nama_lengkap']) ?>')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Belum Ada Siswa</h5>
            <p class="text-muted">Kelas ini belum memiliki siswa untuk tahun pelajaran <?= htmlspecialchars($tahun['tahun_ajaran']) ?></p>
            <a href="add_siswa.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Siswa Pertama
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>

<script>
function confirmDelete(id, nama) {
    if (confirm(`Apakah Anda yakin ingin menghapus siswa "${nama}"?\n\nTindakan ini tidak dapat dibatalkan.`)) {
        window.location.href = `delete_siswa.php?id=${id}&kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>`;
    }
}
</script>

<?php include '../includes/footer.php'; ?>
