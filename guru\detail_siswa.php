<?php
// guru/detail_siswa.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

$id_siswa = $_GET['id'] ?? null;

if (!$id_siswa) {
    die('<div class="alert alert-danger">ID siswa tidak ditemukan.</div>');
}

// Ambil data siswa
$stmt = $conn->prepare("SELECT * FROM siswa WHERE id_siswa = ?");
$stmt->bind_param("i", $id_siswa);
$stmt->execute();
$siswa = $stmt->get_result()->fetch_assoc();

if (!$siswa) {
    die('<div class="alert alert-danger">Data siswa tidak ditemukan.</div>');
}

// Cek apakah user dapat mengakses kelas siswa ini
requireKelasAccess($conn, $siswa['id_kelas'], $siswa['id_tahun']);

// Ambil nama kelas
$stmt = $conn->prepare("SELECT nama_kelas FROM kelas WHERE id_kelas = ?");
$stmt->bind_param("i", $siswa['id_kelas']);
$stmt->execute();
$kelas = $stmt->get_result()->fetch_assoc();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h3><i class="fas fa-user"></i> Detail Siswa - <?= htmlspecialchars($kelas['nama_kelas']) ?></h3>
        <?php
        // Get tahun pelajaran
        $stmt = $conn->prepare("SELECT tahun_ajaran FROM tahun_pelajaran WHERE id_tahun = ?");
        $stmt->bind_param("i", $siswa['id_tahun']);
        $stmt->execute();
        $tahun = $stmt->get_result()->fetch_assoc();
        ?>
        <p class="text-muted mb-0">Tahun Pelajaran: <?= htmlspecialchars($tahun['tahun_ajaran']) ?></p>
    </div>
    <div>
        <a href="data_siswa.php?kelas=<?= $siswa['id_kelas'] ?>&tahun=<?= $siswa['id_tahun'] ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
        <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'wali_kelas'): ?>
            <a href="edit_siswa.php?id=<?= $siswa['id_siswa'] ?>&kelas=<?= $siswa['id_kelas'] ?>&tahun=<?= $siswa['id_tahun'] ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit Siswa
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-3">
                    <img src="../uploads/<?= htmlspecialchars($siswa['foto'] ?: 'placeholder.jpg') ?>"
                         class="img-thumbnail rounded-circle"
                         style="width: 200px; height: 200px; object-fit: cover;"
                         alt="Foto <?= htmlspecialchars($siswa['nama_lengkap']) ?>"
                         onerror="this.src='../uploads/placeholder.jpg'">
                </div>

                <h4 class="card-title text-primary"><?= htmlspecialchars($siswa['nama_lengkap']) ?></h4>
                <?php if ($siswa['nama_panggilan']): ?>
                <p class="text-muted mb-3">"<?= htmlspecialchars($siswa['nama_panggilan']) ?>"</p>
                <?php endif; ?>

                <div class="mb-3">
                    <?php if ($siswa['is_potensial']): ?>
                        <span class="badge bg-success me-1 mb-1">
                            <i class="fas fa-star"></i> Anak Potensial
                        </span>
                    <?php endif; ?>
                    <?php if ($siswa['is_catatan_khusus']): ?>
                        <span class="badge bg-warning mb-1">
                            <i class="fas fa-exclamation-triangle"></i> Catatan Khusus
                        </span>
                    <?php endif; ?>
                    <?php if (!$siswa['is_potensial'] && !$siswa['is_catatan_khusus']): ?>
                        <span class="text-muted">-</span>
                    <?php endif; ?>
                </div>

                <div class="d-grid gap-2">
                    <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'wali_kelas'): ?>
                    <a href="edit_siswa.php?id=<?= $siswa['id_siswa'] ?>&kelas=<?= $siswa['id_kelas'] ?>&tahun=<?= $siswa['id_tahun'] ?>"
                       class="btn btn-warning">
                        <i class="fas fa-edit"></i> Edit Data Siswa
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> Informasi Pribadi</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">
                                <i class="fas fa-church"></i> Asal Paroki:
                            </label>
                            <p class="fw-bold"><?= htmlspecialchars($siswa['asal_paroki'] ?: 'Tidak diisi') ?></p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">
                                <i class="fas fa-map-marker-alt"></i> Keuskupan:
                            </label>
                            <p class="fw-bold"><?= htmlspecialchars($siswa['keuskupan'] ?: 'Tidak diisi') ?></p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label text-muted">
                                <i class="fas fa-clipboard-check"></i> Status PPDB:
                            </label>
                            <p>
                                <span class="badge bg-<?= $siswa['status_ppdb'] == 'Pantas' ? 'success' : ($siswa['status_ppdb'] == 'Diterima' ? 'primary' : 'warning') ?> fs-6">
                                    <?= htmlspecialchars($siswa['status_ppdb'] ?: 'Tidak diisi') ?>
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">
                                <i class="fas fa-school"></i> Kelas:
                            </label>
                            <p class="fw-bold"><?= htmlspecialchars($kelas['nama_kelas']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-clipboard-list"></i> Gambaran Umum</h5>
            </div>
            <div class="card-body">
                <p><?= nl2br(htmlspecialchars($siswa['gambaran_umum'] ?? 'Belum ada data.')) ?></p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-heartbeat"></i> Kondisi Saat Ini</h5>
            </div>
            <div class="card-body">
                <p><?= nl2br(htmlspecialchars($siswa['kondisi_saat_ini'] ?? 'Belum ada data.')) ?></p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-sticky-note"></i> Catatan</h5>
            </div>
            <div class="card-body">
                <p><?= nl2br(htmlspecialchars($siswa['catatan'] ?? 'Belum ada catatan.')) ?></p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-hands-helping"></i> Pendampingan</h5>
            </div>
            <div class="card-body">
                <p><?= nl2br(htmlspecialchars($siswa['pendampingan'] ?? 'Belum ada data pendampingan.')) ?></p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> Hasil Pendampingan</h5>
            </div>
            <div class="card-body">
                <p><?= nl2br(htmlspecialchars($siswa['hasil_pendampingan'] ?? 'Belum ada hasil pendampingan.')) ?></p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-clipboard-list"></i> Catatan Lain</h5>
            </div>
            <div class="card-body">
                <p><?= nl2br(htmlspecialchars($siswa['catatan_lain'] ?? 'Belum ada catatan lain.')) ?></p>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-user-graduate"></i> Catatan Kepamongan</h5>
            </div>
            <div class="card-body">
                <p><?= nl2br(htmlspecialchars($siswa['catatan_kepamongan'] ?? 'Belum ada catatan kepamongan.')) ?></p>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>