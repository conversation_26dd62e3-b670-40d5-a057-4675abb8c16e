-- Quick Fix untuk <PERSON><PERSON>bara<PERSON>
-- Copy paste script ini ke phpMyAdmin dan jalankan

-- <PERSON><PERSON> semua kolom yang diperlukan
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS hasil_komitmen_bersama TEXT AFTER tingkat;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS aspek_sanitas TEXT AFTER hasil_komitmen_bersama;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS aspek_sactitas TEXT AFTER aspek_sanitas;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS aspek_scientia TEXT AFTER aspek_sactitas;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS mapel_agama TEXT AFTER aspek_scientia;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS mapel_bahasa_indonesia TEXT AFTER mapel_agama;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS mapel_bahasa_inggris TEXT AFTER mapel_bahasa_indonesia;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS mapel_bahasa_latin TEXT AFTER mapel_bahasa_inggris;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS mapel_lain TEXT AFTER mapel_bahasa_latin;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER mapel_lain;
ALTER TABLE kelas ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at;

-- Update existing records
UPDATE kelas SET 
    hasil_komitmen_bersama = COALESCE(hasil_komitmen_bersama, ''),
    aspek_sanitas = COALESCE(aspek_sanitas, ''),
    aspek_sactitas = COALESCE(aspek_sactitas, ''),
    aspek_scientia = COALESCE(aspek_scientia, ''),
    mapel_agama = COALESCE(mapel_agama, ''),
    mapel_bahasa_indonesia = COALESCE(mapel_bahasa_indonesia, ''),
    mapel_bahasa_inggris = COALESCE(mapel_bahasa_inggris, ''),
    mapel_bahasa_latin = COALESCE(mapel_bahasa_latin, ''),
    mapel_lain = COALESCE(mapel_lain, '');

-- Cek hasil
SELECT 'Fix completed successfully' as status;
DESCRIBE kelas;
