/**
 * Modern Interactions for Cura Personalis
 * Enhanced UI interactions and animations
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== ENHANCED CARD INTERACTIONS =====
    initCardInteractions();
    
    // ===== SMOOTH SCROLLING =====
    initSmoothScrolling();
    
    // ===== LOADING STATES =====
    initLoadingStates();
    
    // ===== NOTIFICATION SYSTEM =====
    initNotificationSystem();
    
    // ===== ENHANCED TOOLTIPS =====
    initTooltips();
    
    // ===== SEARCH ENHANCEMENTS =====
    initSearchEnhancements();
    
    // ===== FORM ENHANCEMENTS =====
    initFormEnhancements();
});

/**
 * Enhanced Card Interactions
 */
function initCardInteractions() {
    const cards = document.querySelectorAll('.card, .class-card, .stats-card');
    
    cards.forEach(card => {
        // Add ripple effect on click
        card.addEventListener('click', function(e) {
            if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON') return;
            
            const ripple = document.createElement('div');
            const rect = card.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(102, 126, 234, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 1;
            `;
            
            card.style.position = 'relative';
            card.style.overflow = 'hidden';
            card.appendChild(ripple);
            
            setTimeout(() => ripple.remove(), 600);
        });
        
        // Enhanced hover effects
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

/**
 * Smooth Scrolling
 */
function initSmoothScrolling() {
    const links = document.querySelectorAll('a[href^="#"]');
    
    links.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * Loading States
 */
function initLoadingStates() {
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                submitBtn.disabled = true;
                
                // Re-enable after 5 seconds as fallback
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            }
        });
    });
}

/**
 * Notification System
 */
function initNotificationSystem() {
    window.showNotification = function(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification-modern ${type}`;
        notification.innerHTML = `
            <div class="d-flex align-items-start">
                <div class="flex-shrink-0 me-3">
                    <i class="fas ${getNotificationIcon(type)} fa-lg"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="fw-semibold mb-1">${getNotificationTitle(type)}</div>
                    <div class="text-muted">${message}</div>
                </div>
                <button class="btn-close btn-close-sm ms-2" onclick="this.parentElement.parentElement.remove()"></button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => notification.classList.add('show'), 100);
        
        // Auto remove
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, duration);
    };
    
    function getNotificationIcon(type) {
        const icons = {
            success: 'fa-check-circle',
            warning: 'fa-exclamation-triangle',
            error: 'fa-times-circle',
            info: 'fa-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    function getNotificationTitle(type) {
        const titles = {
            success: 'Berhasil!',
            warning: 'Peringatan!',
            error: 'Error!',
            info: 'Informasi'
        };
        return titles[type] || titles.info;
    }
}

/**
 * Enhanced Tooltips
 */
function initTooltips() {
    const tooltipElements = document.querySelectorAll('[data-tooltip]');
    
    tooltipElements.forEach(element => {
        element.classList.add('tooltip-modern');
    });
}

/**
 * Search Enhancements
 */
function initSearchEnhancements() {
    const searchInputs = document.querySelectorAll('input[type="search"], .search-input');
    
    searchInputs.forEach(input => {
        const container = input.parentElement;
        container.classList.add('search-modern');
        
        // Add search icon if not exists
        if (!container.querySelector('.search-icon')) {
            const icon = document.createElement('i');
            icon.className = 'fas fa-search search-icon';
            container.appendChild(icon);
        }
        
        // Real-time search feedback
        input.addEventListener('input', function() {
            const value = this.value.trim();
            if (value.length > 0) {
                this.classList.add('has-value');
            } else {
                this.classList.remove('has-value');
            }
        });
    });
}

/**
 * Form Enhancements
 */
function initFormEnhancements() {
    const formControls = document.querySelectorAll('.form-control, .form-select');
    
    formControls.forEach(control => {
        // Floating label effect
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        control.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
        
        // Initial state
        if (control.value) {
            control.parentElement.classList.add('focused');
        }
    });
    
    // Enhanced file inputs
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const fileName = this.files[0]?.name || 'No file chosen';
            const label = this.parentElement.querySelector('.file-label');
            if (label) {
                label.textContent = fileName;
            }
        });
    });
}

/**
 * Utility Functions
 */

// Add CSS animation keyframes
const style = document.createElement('style');
style.textContent = `
    @keyframes ripple {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
    
    .form-floating.focused label {
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: var(--primary-color);
    }
    
    .search-modern .form-control.has-value {
        padding-left: 3.5rem;
    }
`;
document.head.appendChild(style);

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('animate-in');
        }
    });
}, observerOptions);

// Observe elements with animation classes
document.querySelectorAll('.fade-in-up, .fade-in-left, .fade-in-right, .scale-in').forEach(el => {
    observer.observe(el);
});
