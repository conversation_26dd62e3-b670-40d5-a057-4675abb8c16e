-- <PERSON><PERSON>t untuk menambahkan fitur kondisi akademik
-- Jalankan script ini di phpMyAdmin atau MySQL

-- 1. Tam<PERSON> kolom nilai rata-rata pada tabel siswa
ALTER TABLE siswa ADD COLUMN nilai_rata_rata DECIMAL(5,2) DEFAULT NULL AFTER hasil_pendampingan;

-- 2. <PERSON>ikan tabel kelas_gambaran_umum sudah ada (sudah ada di install.sql)
-- Jika belum ada, buat tabel ini:
CREATE TABLE IF NOT EXISTS kelas_gambaran_umum (
    id INT PRIMARY KEY AUTO_INCREMENT,
    id_kelas INT NOT NULL,
    id_tahun INT NOT NULL,
    komitmen_bersama TEXT,
    rata_rata_nilai DECIMAL(5,2),
    nilai_tertinggi DECIMAL(5,2),
    nilai_terendah DECIMAL(5,2),
    rangking_5_terbawah JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (id_kelas) REFERENCES kelas(id_kelas),
    FOREIGN KEY (id_tahun) REFERENCES tahun_pelajaran(id_tahun),
    UNIQUE KEY unique_kelas_tahun (id_kelas, id_tahun)
);

-- 3. Insert data contoh nilai untuk testing (opsional)
-- Uncomment baris di bawah jika ingin menambah data contoh
/*
UPDATE siswa SET nilai_rata_rata = 85.5 WHERE id_siswa = 1;
UPDATE siswa SET nilai_rata_rata = 78.2 WHERE id_siswa = 2;
UPDATE siswa SET nilai_rata_rata = 92.1 WHERE id_siswa = 3;
UPDATE siswa SET nilai_rata_rata = 67.8 WHERE id_siswa = 4;
UPDATE siswa SET nilai_rata_rata = 73.5 WHERE id_siswa = 5;
UPDATE siswa SET nilai_rata_rata = 88.9 WHERE id_siswa = 6;
UPDATE siswa SET nilai_rata_rata = 65.2 WHERE id_siswa = 7;
UPDATE siswa SET nilai_rata_rata = 79.4 WHERE id_siswa = 8;
UPDATE siswa SET nilai_rata_rata = 81.7 WHERE id_siswa = 9;
UPDATE siswa SET nilai_rata_rata = 76.3 WHERE id_siswa = 10;
*/

-- 4. Cek hasil
SELECT 'Academic features added successfully!' as status;

-- Tampilkan struktur tabel siswa untuk verifikasi
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'siswa' 
  AND TABLE_SCHEMA = DATABASE()
  AND COLUMN_NAME = 'nilai_rata_rata'
ORDER BY ORDINAL_POSITION;

-- Tampilkan struktur tabel kelas_gambaran_umum
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'kelas_gambaran_umum' 
  AND TABLE_SCHEMA = DATABASE()
ORDER BY ORDINAL_POSITION;
