/* Custom CSS for Cura Personalis */

/* Additional custom styles can be added here */

/* Ensure text visibility on all elements */
* {
    color: inherit;
}

/* Fix any remaining text visibility issues */
.btn-primary,
.btn-success,
.btn-warning,
.btn-info,
.btn-danger {
    color: white !important;
}

.btn-primary *,
.btn-success *,
.btn-warning *,
.btn-info *,
.btn-danger * {
    color: white !important;
}

/* Ensure card text is visible */
.card-body {
    color: var(--gray-700, #374151) !important;
}

.card-title {
    color: var(--gray-900, #111827) !important;
}

.card-text {
    color: var(--gray-600, #4b5563) !important;
}

/* Stats card text fixes */
.stats-card .stats-number {
    color: inherit !important;
}

.stats-card .stats-label {
    color: var(--gray-600, #4b5563) !important;
}

/* Class card text fixes */
.class-card .class-title {
    color: var(--gray-900, #111827) !important;
}

.class-card .class-subtitle {
    color: var(--gray-500, #6b7280) !important;
}

/* Wali info text fixes */
.wali-info .wali-label {
    color: var(--gray-500, #6b7280) !important;
}

.wali-info .wali-name {
    color: var(--gray-900, #111827) !important;
}

/* Navbar text fixes */
.navbar-brand {
    color: var(--primary-color, #667eea) !important;
}

.nav-link {
    color: var(--gray-700, #374151) !important;
}

/* Footer text fixes */
footer {
    color: white !important;
    margin-top: 50px !important;
    border-top: 3px solid #667eea !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
}

footer * {
    color: white !important;
}

footer h6 {
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
    font-weight: 700 !important;
}

footer small {
    color: #e2e8f0 !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

footer .text-danger {
    color: #ff6b6b !important;
    filter: brightness(1.2) !important;
}

footer .text-warning {
    color: #ffd700 !important;
    filter: brightness(1.1) !important;
}

/* Ensure footer visibility */
footer {
    position: relative !important;
    z-index: 100 !important;
    min-height: 80px !important;
}

/* Badge text fixes */
.badge {
    color: white !important;
    font-weight: 600 !important;
    min-width: 60px !important;
    text-align: center !important;
}

/* Specific badge color fixes */
.badge.bg-success {
    background-color: #198754 !important;
    color: white !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
    color: white !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
    color: white !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000 !important;
}

/* Remove any opacity that makes badges invisible */
.badge[class*="bg-opacity"] {
    background-color: inherit !important;
    opacity: 1 !important;
}

/* Alert text fixes */
.alert {
    color: inherit !important;
}

/* Table text fixes */
.table {
    color: var(--gray-700, #374151) !important;
}

.table th {
    color: white !important;
}

/* Form text fixes */
.form-label {
    color: var(--gray-700, #374151) !important;
}

.form-control {
    color: var(--gray-900, #111827) !important;
}

/* Dropdown text fixes */
.dropdown-item {
    color: var(--gray-700, #374151) !important;
}

.dropdown-header {
    color: var(--gray-700, #374151) !important;
}

/* Additional utility classes */
.text-visible {
    color: var(--gray-900, #111827) !important;
}

.text-visible-light {
    color: var(--gray-600, #4b5563) !important;
}

.text-visible-muted {
    color: var(--gray-500, #6b7280) !important;
}

/* Table header text shadow for better contrast */
.shadow-text {
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7) !important;
}

/* Enhanced table header styling */
.table thead th {
    color: white !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8) !important;
    font-weight: 600 !important;
    background-color: #2c3e50 !important;
}

/* Specific styling for data siswa table headers */
.table thead tr th {
    color: white !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.9) !important;
    font-weight: 700 !important;
    letter-spacing: 0.5px !important;
    padding: 12px 8px !important;
}

/* Force visibility for problematic columns */
.table thead th:nth-child(7), /* Status PPDB */
.table thead th:nth-child(8)  /* Status Khusus */ {
    color: #ffffff !important;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 1) !important;
    background-color: #1a202c !important;
    font-weight: 800 !important;
}
