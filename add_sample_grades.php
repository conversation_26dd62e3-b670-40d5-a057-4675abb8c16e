<?php
// Script untuk menambahkan data contoh nilai siswa
// Jalankan file ini di browser untuk menambah data contoh

include 'includes/db.php';

echo "<h2>Menambahkan Data Contoh Nilai Siswa</h2>";

// Cek apakah kolom nilai_rata_rata sudah ada
$check_column = $conn->query("SHOW COLUMNS FROM siswa LIKE 'nilai_rata_rata'");
if ($check_column->num_rows == 0) {
    echo "<div style='color: red; font-weight: bold;'>❌ Kolom 'nilai_rata_rata' belum ada!</div>";
    echo "<p>Silakan jalankan script SQL 'add_academic_features.sql' terlebih dahulu.</p>";
    exit;
}

echo "<div style='color: green; font-weight: bold;'>✅ Kolom 'nilai_rata_rata' sudah ada</div>";

// Ambil semua siswa yang belum memiliki nilai
$result = $conn->query("SELECT id_siswa, nama_lengkap, id_kelas FROM siswa WHERE nilai_rata_rata IS NULL ORDER BY id_kelas, nama_lengkap");

if ($result->num_rows == 0) {
    echo "<p><strong>Semua siswa sudah memiliki nilai rata-rata.</strong></p>";
    
    // Tampilkan data yang sudah ada
    $result = $conn->query("SELECT s.nama_lengkap, s.nilai_rata_rata, k.nama_kelas FROM siswa s JOIN kelas k ON s.id_kelas = k.id_kelas WHERE s.nilai_rata_rata IS NOT NULL ORDER BY k.nama_kelas, s.nama_lengkap");
    
    if ($result->num_rows > 0) {
        echo "<h3>Data Nilai yang Sudah Ada:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background-color: #f0f0f0;'><th>Nama Siswa</th><th>Kelas</th><th>Nilai Rata-rata</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['nama_lengkap']) . "</td>";
            echo "<td>" . htmlspecialchars($row['nama_kelas']) . "</td>";
            echo "<td style='text-align: center;'>" . number_format($row['nilai_rata_rata'], 1) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    exit;
}

echo "<p>Ditemukan <strong>" . $result->num_rows . "</strong> siswa yang belum memiliki nilai.</p>";

// Generate nilai random untuk setiap siswa
$updated = 0;
$errors = 0;

echo "<h3>Menambahkan Nilai Random:</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f0f0f0;'><th>Nama Siswa</th><th>Kelas</th><th>Nilai yang Ditambahkan</th><th>Status</th></tr>";

while ($row = $result->fetch_assoc()) {
    // Generate nilai random antara 60-95 dengan 1 desimal
    $nilai_random = round(rand(600, 950) / 10, 1);
    
    // Update nilai siswa
    $stmt = $conn->prepare("UPDATE siswa SET nilai_rata_rata = ? WHERE id_siswa = ?");
    $stmt->bind_param("di", $nilai_random, $row['id_siswa']);
    
    // Ambil nama kelas
    $kelas_result = $conn->query("SELECT nama_kelas FROM kelas WHERE id_kelas = " . $row['id_kelas']);
    $kelas_name = $kelas_result->fetch_assoc()['nama_kelas'];
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($row['nama_lengkap']) . "</td>";
    echo "<td>" . htmlspecialchars($kelas_name) . "</td>";
    echo "<td style='text-align: center;'>" . $nilai_random . "</td>";
    
    if ($stmt->execute()) {
        echo "<td style='color: green;'>✅ Berhasil</td>";
        $updated++;
    } else {
        echo "<td style='color: red;'>❌ Gagal: " . $conn->error . "</td>";
        $errors++;
    }
    echo "</tr>";
}

echo "</table>";

echo "<div style='margin-top: 20px; padding: 15px; background-color: #e8f5e8; border: 1px solid #4CAF50; border-radius: 5px;'>";
echo "<h3 style='color: #2E7D32; margin-top: 0;'>Ringkasan:</h3>";
echo "<p><strong>Berhasil diupdate:</strong> $updated siswa</p>";
if ($errors > 0) {
    echo "<p><strong>Gagal:</strong> $errors siswa</p>";
}
echo "</div>";

// Tampilkan statistik per kelas
echo "<h3>Statistik Nilai per Kelas:</h3>";
$stats_result = $conn->query("
    SELECT k.nama_kelas, 
           COUNT(s.id_siswa) as total_siswa,
           COUNT(s.nilai_rata_rata) as siswa_dengan_nilai,
           ROUND(AVG(s.nilai_rata_rata), 1) as rata_rata,
           ROUND(MAX(s.nilai_rata_rata), 1) as nilai_tertinggi,
           ROUND(MIN(s.nilai_rata_rata), 1) as nilai_terendah
    FROM kelas k 
    LEFT JOIN siswa s ON k.id_kelas = s.id_kelas 
    GROUP BY k.id_kelas, k.nama_kelas 
    ORDER BY k.nama_kelas
");

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f0f0f0;'>";
echo "<th>Kelas</th><th>Total Siswa</th><th>Siswa dengan Nilai</th><th>Rata-rata</th><th>Tertinggi</th><th>Terendah</th>";
echo "</tr>";

while ($row = $stats_result->fetch_assoc()) {
    echo "<tr>";
    echo "<td>" . htmlspecialchars($row['nama_kelas']) . "</td>";
    echo "<td style='text-align: center;'>" . $row['total_siswa'] . "</td>";
    echo "<td style='text-align: center;'>" . $row['siswa_dengan_nilai'] . "</td>";
    echo "<td style='text-align: center;'>" . ($row['rata_rata'] ?: '-') . "</td>";
    echo "<td style='text-align: center;'>" . ($row['nilai_tertinggi'] ?: '-') . "</td>";
    echo "<td style='text-align: center;'>" . ($row['nilai_terendah'] ?: '-') . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<div style='margin-top: 20px; padding: 15px; background-color: #e3f2fd; border: 1px solid #2196F3; border-radius: 5px;'>";
echo "<h3 style='color: #1976D2; margin-top: 0;'>Langkah Selanjutnya:</h3>";
echo "<p>1. Buka halaman <strong>Gambaran Kelas</strong> untuk melihat kondisi akademik</p>";
echo "<p>2. Data nilai dapat diedit melalui halaman <strong>Edit Siswa</strong></p>";
echo "<p>3. Admin dapat menambah siswa baru dengan nilai melalui halaman <strong>Tambah Siswa</strong></p>";
echo "</div>";

$conn->close();
?>
