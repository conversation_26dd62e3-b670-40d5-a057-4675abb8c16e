# 📸 Panduan Upload Foto Siswa

## 🎯 **Fitur Upload Foto**

### **1. Lokasi Upload Foto:**
- **Admin:** `admin/add_siswa.php` dan `admin/edit_siswa.php`
- **Wali Kelas:** `guru/edit_siswa.php`

### **2. Spesifikasi Foto:**
- **Format:** JPG, PNG, GIF
- **Ukuran Maksimal:** 2MB
- **Resolusi:** Otomatis diresize ke 400x400 pixel
- **Folder Penyimpanan:** `uploads/`

### **3. Cara Upload Foto:**

#### **Tambah Siswa Baru (Admin):**
1. Login sebagai Admin
2. Dashboard → "Tambah Siswa"
3. Isi data siswa
4. <PERSON> bagian "Foto Siswa" → klik "Upload Foto"
5. Pilih file foto (JPG/PNG/GIF)
6. Preview foto akan muncul otomatis
7. <PERSON><PERSON> "Simpan Siswa"

#### **Edit Foto Siswa (Admin/Wali Kelas):**
1. <PERSON>gin sebagai Admin atau Wali Kelas
2. <PERSON><PERSON><PERSON> ke "Data Siswa" → klik "Edit" (✏️)
3. Di bagian "Foto Siswa":
   - **Upload Baru:** Pilih file foto baru
   - **Hapus Foto:** Centang "Hapus foto yang ada"
4. Preview foto akan berubah otomatis
5. Klik "Simpan Perubahan"

### **4. Fitur Foto:**

#### **✅ Preview Real-time:**
- Foto langsung terlihat setelah dipilih
- Tidak perlu save dulu untuk melihat preview

#### **✅ Auto Resize:**
- Foto otomatis diresize ke 400x400 pixel
- Kualitas tetap bagus dengan kompresi optimal
- Menghemat storage server

#### **✅ Validasi File:**
- Hanya menerima format JPG, PNG, GIF
- Maksimal ukuran 2MB
- Error message jelas jika file tidak valid

#### **✅ Manajemen File:**
- Foto lama otomatis dihapus saat upload baru
- Nama file unik untuk menghindari konflik
- Placeholder default untuk siswa tanpa foto

### **5. Struktur File:**

```
uploads/
├── placeholder.jpg          # Default foto untuk siswa tanpa foto
├── siswa_1_1640995200.jpg  # Format: siswa_{id}_{timestamp}.{ext}
├── siswa_2_1640995300.png
└── siswa_new_1640995400.jpg # Format untuk siswa baru
```

### **6. Troubleshooting:**

#### **❌ "Format file tidak didukung"**
- **Solusi:** Gunakan file JPG, PNG, atau GIF
- **Cek:** Extension file harus benar (.jpg, .png, .gif)

#### **❌ "Ukuran file terlalu besar"**
- **Solusi:** Kompres foto atau gunakan foto dengan resolusi lebih kecil
- **Maksimal:** 2MB (2.048.000 bytes)

#### **❌ "Gagal mengupload foto"**
- **Solusi:** 
  - Cek permission folder `uploads/` (harus writable)
  - Pastikan PHP extension GD aktif
  - Cek disk space server

#### **❌ Foto tidak muncul**
- **Solusi:**
  - Cek apakah file ada di folder `uploads/`
  - Refresh browser (Ctrl+F5)
  - Cek path foto di database

### **7. Keamanan:**

#### **✅ Validasi Ketat:**
- Hanya menerima format image yang aman
- Validasi MIME type dan extension
- Ukuran file dibatasi

#### **✅ Nama File Aman:**
- Nama file di-generate otomatis
- Tidak menggunakan nama file original
- Timestamp untuk uniqueness

#### **✅ Folder Terproteksi:**
- Folder `uploads/` tidak bisa diakses langsung
- Hanya file image yang diizinkan

### **8. Tips Penggunaan:**

#### **📷 Foto Terbaik:**
- Gunakan foto portrait (vertikal)
- Background polos lebih baik
- Wajah terlihat jelas
- Resolusi minimal 200x200 pixel

#### **⚡ Performance:**
- Foto otomatis dioptimasi
- Loading halaman tetap cepat
- Storage efisien

#### **🔄 Backup:**
- Backup folder `uploads/` secara berkala
- Foto tidak tersimpan di database (hanya nama file)

### **9. Contoh Penggunaan:**

```php
// Cara akses foto di template
<img src="../uploads/<?= htmlspecialchars($siswa['foto'] ?: 'placeholder.jpg') ?>" 
     alt="Foto <?= htmlspecialchars($siswa['nama_lengkap']) ?>"
     class="img-thumbnail" 
     style="width: 150px; height: 150px; object-fit: cover;"
     onerror="this.src='../uploads/placeholder.jpg'">
```

### **10. Update Database:**

Pastikan kolom `foto` sudah ada di tabel `siswa`:

```sql
-- Jika belum ada kolom foto
ALTER TABLE siswa ADD COLUMN foto VARCHAR(255) DEFAULT 'placeholder.jpg' AFTER hasil_pendampingan;

-- Update existing records
UPDATE siswa SET foto = 'placeholder.jpg' WHERE foto IS NULL OR foto = '';
```

---

**🎉 Sekarang sistem upload foto sudah lengkap dan siap digunakan!**
