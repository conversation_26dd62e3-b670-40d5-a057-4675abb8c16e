# Fitur Kondisi Akademik - Cura Personalis

## Deskripsi
Fitur ini menambahkan kemampuan untuk mengelola dan menampilkan kondisi akademik siswa dalam aplikasi Cura Personalis. Fitur ini mencakup:

- **Rata-rata nilai siswa**: Menampilkan nilai rata-rata keseluruhan siswa dalam kelas
- **Nilai tertinggi**: Menampilkan nilai tertinggi siswa dalam kelas  
- **<PERSON><PERSON> terendah**: Menampilkan nilai terendah siswa dalam kelas
- **Ranking 5 terbawah**: Menampilkan nama dan nilai rata-rata 5 siswa dengan nilai terendah

## Instalasi

### 1. Jalankan Script Database
Jalankan script SQL berikut di phpMyAdmin atau MySQL:

```sql
-- Jalankan file: add_academic_features.sql
```

Script ini akan:
- Menambah kolom `nilai_rata_rata` pada tabel `siswa`
- Memastikan tabel `kelas_gambaran_umum` sudah ada (untuk fitur masa depan)

### 2. Tambah Data Contoh (Opsional)
Untuk testing, jalankan file PHP berikut di browser:

```
http://localhost/cura_personalis/add_sample_grades.php
```

File ini akan menambahkan nilai random (60-95) untuk semua siswa yang belum memiliki nilai.

## Fitur yang Ditambahkan

### 1. Halaman Gambaran Kelas (`guru/gambaran_kelas.php`)
- **Section Kondisi Akademik** baru ditambahkan setelah section Data Demografi
- Menampilkan statistik akademik kelas:
  - Rata-rata nilai siswa
  - Nilai tertinggi
  - Nilai terendah
  - Ranking 5 terbawah dengan nama dan nilai
- Menampilkan informasi jumlah siswa yang memiliki nilai
- Jika belum ada data nilai, menampilkan pesan informatif

### 2. Form Edit Siswa (`guru/edit_siswa.php`)
- **Field Nilai Rata-rata** ditambahkan di section Data Pendampingan
- Input type: number dengan range 0-100, step 0.1
- Field opsional (boleh kosong)
- Dapat diakses oleh wali kelas dan admin

### 3. Form Tambah Siswa (`admin/add_siswa.php`)
- **Field Nilai Rata-rata** ditambahkan di section Data Pendampingan
- Input type: number dengan range 0-100, step 0.1
- Field opsional (boleh kosong)
- Hanya dapat diakses oleh admin

## Cara Penggunaan

### Untuk Admin:
1. **Menambah Siswa Baru dengan Nilai**:
   - Buka menu Admin → Tambah Siswa
   - Isi data siswa seperti biasa
   - Isi field "Nilai Rata-rata" jika sudah ada nilai
   - Simpan data

2. **Mengedit Nilai Siswa**:
   - Buka menu Admin → Manage Siswa → Edit
   - Scroll ke bagian "Data Pendampingan"
   - Edit field "Nilai Rata-rata"
   - Simpan perubahan

### Untuk Wali Kelas:
1. **Mengedit Nilai Siswa di Kelas**:
   - Buka menu Data Siswa
   - Klik "Edit" pada siswa yang ingin diedit
   - Scroll ke bagian "Data Pendampingan"
   - Edit field "Nilai Rata-rata"
   - Simpan perubahan

2. **Melihat Kondisi Akademik Kelas**:
   - Buka menu Gambaran Kelas
   - Lihat section "Kondisi Akademik"
   - Data akan otomatis terhitung berdasarkan nilai siswa

## Struktur Database

### Tabel `siswa`
Kolom baru yang ditambahkan:
```sql
nilai_rata_rata DECIMAL(5,2) DEFAULT NULL
```

### Tabel `kelas_gambaran_umum` (sudah ada)
Tabel ini sudah disiapkan untuk fitur akademik lanjutan:
```sql
rata_rata_nilai DECIMAL(5,2)
nilai_tertinggi DECIMAL(5,2)
nilai_terendah DECIMAL(5,2)
rangking_5_terbawah JSON
```

## Validasi dan Keamanan

1. **Input Validation**:
   - Nilai harus antara 0-100
   - Menggunakan type="number" dengan step 0.1
   - Field opsional (boleh NULL)

2. **Database Security**:
   - Menggunakan prepared statements
   - Proper data binding dengan tipe yang sesuai
   - Validasi role user (admin/wali_kelas)

3. **Display Security**:
   - Semua output menggunakan htmlspecialchars()
   - Number formatting untuk konsistensi tampilan

## Tampilan UI

### Section Kondisi Akademik
- **Layout**: Card dengan header gradient biru
- **Statistik**: 3 kolom untuk rata-rata, tertinggi, terendah
- **Ranking**: Panel terpisah untuk 5 siswa terbawah
- **Info**: Menampilkan jumlah siswa dengan nilai
- **Empty State**: Pesan informatif jika belum ada data

### Form Input Nilai
- **Field**: Input number dengan label jelas
- **Placeholder**: Contoh nilai (85.5)
- **Help Text**: Instruksi penggunaan
- **Validation**: HTML5 validation + server-side

## Troubleshooting

### Error: Column 'nilai_rata_rata' doesn't exist
**Solusi**: Jalankan script `add_academic_features.sql`

### Data nilai tidak muncul di Gambaran Kelas
**Solusi**: 
1. Pastikan siswa sudah memiliki nilai (tidak NULL)
2. Cek apakah ada siswa di kelas tersebut
3. Refresh halaman

### Nilai tidak tersimpan saat edit siswa
**Solusi**:
1. Cek apakah kolom database sudah ditambahkan
2. Pastikan form menggunakan method POST
3. Cek error di browser console atau PHP error log

## Pengembangan Selanjutnya

Fitur yang dapat ditambahkan:
1. **Import/Export Nilai**: Upload file Excel untuk batch input nilai
2. **Grafik Akademik**: Visualisasi distribusi nilai dengan Chart.js
3. **Laporan Akademik**: Generate PDF laporan kondisi akademik
4. **Tracking Perkembangan**: Menyimpan history nilai per semester
5. **Integrasi dengan Sistem Nilai**: Koneksi dengan sistem akademik sekolah

## File yang Dimodifikasi

1. `add_academic_features.sql` - Script database baru
2. `guru/gambaran_kelas.php` - Tambah section kondisi akademik
3. `guru/edit_siswa.php` - Tambah field nilai rata-rata
4. `admin/add_siswa.php` - Tambah field nilai rata-rata
5. `add_sample_grades.php` - Utility untuk data contoh
6. `FITUR_KONDISI_AKADEMIK.md` - Dokumentasi ini
