<?php
// check_foto_column.php - Cek dan tambah kolom foto jika belum ada

include 'includes/db.php';

echo "<h2>🔍 Cek Kolom Foto di Tabel Siswa</h2>";

// Cek struktur tabel siswa
$result = $conn->query("DESCRIBE siswa");
$columns = [];
$foto_exists = false;

echo "<h3>Struktur Tabel Siswa:</h3>";
echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";

while ($row = $result->fetch_assoc()) {
    $columns[] = $row['Field'];
    if ($row['Field'] === 'foto') {
        $foto_exists = true;
    }
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "</tr>";
}
echo "</table>";

if ($foto_exists) {
    echo "<div style='color: green; font-weight: bold;'>✅ Kolom 'foto' sudah ada di tabel siswa</div>";
    
    // Cek data foto siswa
    $result = $conn->query("SELECT id_siswa, nama_lengkap, foto FROM siswa LIMIT 5");
    echo "<h3>Sample Data Foto:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Nama</th><th>Foto</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id_siswa'] . "</td>";
        echo "<td>" . $row['nama_lengkap'] . "</td>";
        echo "<td>" . ($row['foto'] ?: 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} else {
    echo "<div style='color: red; font-weight: bold;'>❌ Kolom 'foto' BELUM ada di tabel siswa</div>";
    echo "<p>Menambahkan kolom foto...</p>";
    
    // Tambah kolom foto
    $sql = "ALTER TABLE siswa ADD COLUMN foto VARCHAR(255) DEFAULT 'placeholder.jpg' AFTER hasil_pendampingan";
    
    if ($conn->query($sql)) {
        echo "<div style='color: green;'>✅ Kolom foto berhasil ditambahkan</div>";
        
        // Update existing records
        $update_sql = "UPDATE siswa SET foto = 'placeholder.jpg' WHERE foto IS NULL OR foto = ''";
        if ($conn->query($update_sql)) {
            echo "<div style='color: green;'>✅ Data existing siswa berhasil diupdate</div>";
        } else {
            echo "<div style='color: red;'>❌ Error update data: " . $conn->error . "</div>";
        }
    } else {
        echo "<div style='color: red;'>❌ Error menambah kolom: " . $conn->error . "</div>";
    }
}

// Cek folder uploads
echo "<h3>📁 Cek Folder Uploads:</h3>";
if (is_dir('uploads')) {
    echo "<div style='color: green;'>✅ Folder 'uploads' ada</div>";
    
    if (file_exists('uploads/placeholder.jpg')) {
        echo "<div style='color: green;'>✅ File 'placeholder.jpg' ada</div>";
        echo "<p><img src='uploads/placeholder.jpg' style='width: 100px; height: 100px; object-fit: cover; border: 1px solid #ccc;'></p>";
    } else {
        echo "<div style='color: red;'>❌ File 'placeholder.jpg' tidak ada</div>";
        echo "<p><a href='create_placeholder.php'>🔗 Buat placeholder.jpg</a></p>";
    }
    
    // List files di uploads
    $files = scandir('uploads');
    $image_files = array_filter($files, function($file) {
        return in_array(strtolower(pathinfo($file, PATHINFO_EXTENSION)), ['jpg', 'jpeg', 'png', 'gif']);
    });
    
    echo "<p><strong>File gambar di uploads:</strong> " . count($image_files) . " file</p>";
    foreach ($image_files as $file) {
        echo "<span style='background: #f0f0f0; padding: 2px 5px; margin: 2px; display: inline-block;'>$file</span> ";
    }
    
} else {
    echo "<div style='color: red;'>❌ Folder 'uploads' tidak ada</div>";
    echo "<p>Membuat folder uploads...</p>";
    if (mkdir('uploads', 0755, true)) {
        echo "<div style='color: green;'>✅ Folder uploads berhasil dibuat</div>";
    } else {
        echo "<div style='color: red;'>❌ Gagal membuat folder uploads</div>";
    }
}

echo "<br><br>";
echo "<a href='guru/detail_siswa.php?id=1&kelas=1&tahun=1'>🔗 Test Detail Siswa</a> | ";
echo "<a href='guru/data_siswa.php?kelas=1&tahun=1'>🔗 Test Data Siswa</a> | ";
echo "<a href='login.php'>🔗 Login</a>";
?>
