<?php
// guru/catatan_khusus.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

$id_kelas = $_GET['kelas'] ?? $_SESSION['current_kelas'] ?? null;
$id_tahun = $_GET['tahun'] ?? $_SESSION['current_tahun'] ?? null;

// Validasi parameter dan akses
if (!$id_kelas || !$id_tahun) {
    die('<div class="alert alert-danger">Parameter kelas dan tahun harus diisi.</div>');
}

// Cek apakah user dapat mengakses kelas ini
requireKelasAccess($conn, $id_kelas, $id_tahun);

// Ambil data kelas
$stmt = $conn->prepare("SELECT nama_kelas FROM kelas WHERE id_kelas = ?");
$stmt->bind_param("i", $id_kelas);
$stmt->execute();
$kelas = $stmt->get_result()->fetch_assoc();

// Ambil data siswa catatan khusus
$stmt = $conn->prepare("
    SELECT id_siswa, nama_lengkap, nama_panggilan, asal_paroki, keuskupan,
           catatan, kondisi_saat_ini, pendampingan
    FROM siswa
    WHERE id_kelas = ? AND id_tahun = ? AND is_catatan_khusus = 1
    ORDER BY nama_lengkap
");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$siswa_catatan = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="fas fa-exclamation-triangle text-warning"></i> Anak Catatan Khusus - <?= htmlspecialchars($kelas['nama_kelas']) ?></h3>
    <a href="data_siswa.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Kembali ke Data Siswa
    </a>
</div>

<div class="alert alert-warning">
    <i class="fas fa-exclamation-triangle"></i>
    Daftar siswa yang memerlukan perhatian khusus dalam pendampingan dan pembinaan.
</div>

<?php if ($siswa_catatan->num_rows > 0): ?>
    <div class="row">
        <?php while($siswa = $siswa_catatan->fetch_assoc()): ?>
            <div class="col-md-6 mb-4">
                <div class="card h-100 border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle"></i>
                            <?= htmlspecialchars($siswa['nama_lengkap']) ?>
                            <small class="text-muted">(<?= htmlspecialchars($siswa['nama_panggilan']) ?>)</small>
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Asal Paroki:</strong> <?= htmlspecialchars($siswa['asal_paroki'] ?? '-') ?></p>
                        <p><strong>Keuskupan:</strong> <?= htmlspecialchars($siswa['keuskupan'] ?? '-') ?></p>

                        <?php if ($siswa['catatan']): ?>
                            <div class="mt-3">
                                <strong>Catatan:</strong>
                                <p class="text-muted small"><?= nl2br(htmlspecialchars(substr($siswa['catatan'], 0, 150))) ?><?= strlen($siswa['catatan']) > 150 ? '...' : '' ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if ($siswa['kondisi_saat_ini']): ?>
                            <div class="mt-3">
                                <strong>Kondisi Saat Ini:</strong>
                                <p class="text-muted small"><?= nl2br(htmlspecialchars(substr($siswa['kondisi_saat_ini'], 0, 150))) ?><?= strlen($siswa['kondisi_saat_ini']) > 150 ? '...' : '' ?></p>
                            </div>
                        <?php endif; ?>

                        <?php if ($siswa['pendampingan']): ?>
                            <div class="mt-3">
                                <strong>Pendampingan:</strong>
                                <p class="text-muted small"><?= nl2br(htmlspecialchars(substr($siswa['pendampingan'], 0, 150))) ?><?= strlen($siswa['pendampingan']) > 150 ? '...' : '' ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="card-footer">
                        <a href="detail_siswa.php?id=<?= $siswa['id_siswa'] ?>" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye"></i> Lihat Detail
                        </a>
                    </div>
                </div>
            </div>
        <?php endwhile; ?>
    </div>
<?php else: ?>
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        Belum ada siswa yang ditandai sebagai catatan khusus di kelas ini.
    </div>
<?php endif; ?>

<?php include '../includes/footer.php'; ?>