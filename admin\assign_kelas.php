<?php
// admin/assign_kelas.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Aks<PERSON> ditolak. Halaman ini hanya untuk administrator.</div>');
}

$id_user = $_GET['user'] ?? null;
$message = '';

if (!$id_user) {
    die('<div class="alert alert-danger">ID user tidak ditemukan.</div>');
}

// Ambil data user
$stmt = $conn->prepare("SELECT * FROM users WHERE id_user = ? AND role = 'wali_kelas'");
$stmt->bind_param("i", $id_user);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();

if (!$user) {
    die('<div class="alert alert-danger">User tidak ditemukan atau bukan wali kelas.</div>');
}

// Handle form submission
if ($_POST && isset($_POST['action']) && $_POST['action'] === 'update_kelas') {
    $selected_kelas = $_POST['kelas'] ?? [];
    $id_tahun = $_POST['id_tahun'];
    
    // Hapus assignment lama untuk tahun ini
    $stmt = $conn->prepare("DELETE FROM user_kelas WHERE id_user = ? AND id_tahun = ?");
    $stmt->bind_param("ii", $id_user, $id_tahun);
    $stmt->execute();
    
    // Insert assignment baru
    if (!empty($selected_kelas)) {
        $stmt = $conn->prepare("INSERT INTO user_kelas (id_user, id_kelas, id_tahun) VALUES (?, ?, ?)");
        
        foreach ($selected_kelas as $id_kelas) {
            $stmt->bind_param("iii", $id_user, $id_kelas, $id_tahun);
            $stmt->execute();
        }
    }
    
    $message = '<div class="alert alert-success">Assignment kelas berhasil diperbarui.</div>';
}

// Ambil tahun pelajaran
$tahun_list = getAllTahun($conn);
$tahun_aktif = getActiveTahun($conn);
$selected_tahun = $_GET['tahun'] ?? $tahun_aktif['id_tahun'];

// Ambil semua kelas
$stmt = $conn->prepare("SELECT * FROM kelas ORDER BY nama_kelas");
$stmt->execute();
$all_kelas = $stmt->get_result();

// Ambil kelas yang sudah di-assign untuk user dan tahun ini
$stmt = $conn->prepare("SELECT id_kelas FROM user_kelas WHERE id_user = ? AND id_tahun = ?");
$stmt->bind_param("ii", $id_user, $selected_tahun);
$stmt->execute();
$assigned_kelas = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
$assigned_kelas_ids = array_column($assigned_kelas, 'id_kelas');
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="fas fa-school"></i> Atur Kelas - <?= htmlspecialchars($user['nama_lengkap']) ?></h3>
    <a href="manage_users.php" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Kembali
    </a>
</div>

<?= $message ?>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list-check"></i> Assignment Kelas</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="update_kelas">
                    
                    <div class="mb-3">
                        <label class="form-label">Tahun Pelajaran</label>
                        <select name="id_tahun" class="form-control" onchange="window.location.href='assign_kelas.php?user=<?= $id_user ?>&tahun=' + this.value">
                            <?php foreach ($tahun_list as $tahun): ?>
                                <option value="<?= $tahun['id_tahun'] ?>" <?= $tahun['id_tahun'] == $selected_tahun ? 'selected' : '' ?>>
                                    <?= htmlspecialchars($tahun['tahun_ajaran']) ?>
                                    <?= $tahun['is_active'] ? ' (Aktif)' : '' ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Pilih Kelas yang Dapat Diakses</label>
                        <div class="row">
                            <?php while($kelas = $all_kelas->fetch_assoc()): ?>
                                <div class="col-md-6 mb-2">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" 
                                               name="kelas[]" value="<?= $kelas['id_kelas'] ?>"
                                               id="kelas_<?= $kelas['id_kelas'] ?>"
                                               <?= in_array($kelas['id_kelas'], $assigned_kelas_ids) ? 'checked' : '' ?>>
                                        <label class="form-check-label" for="kelas_<?= $kelas['id_kelas'] ?>">
                                            <?= htmlspecialchars($kelas['nama_kelas']) ?>
                                            <?php if ($kelas['tingkat']): ?>
                                                <small class="text-muted">(<?= htmlspecialchars($kelas['tingkat']) ?>)</small>
                                            <?php endif; ?>
                                        </label>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan Assignment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> Info User</h5>
            </div>
            <div class="card-body">
                <p><strong>Username:</strong> <?= htmlspecialchars($user['username']) ?></p>
                <p><strong>Nama:</strong> <?= htmlspecialchars($user['nama_lengkap']) ?></p>
                <p><strong>Email:</strong> <?= htmlspecialchars($user['email'] ?? '-') ?></p>
                <p><strong>Role:</strong> 
                    <span class="badge bg-primary"><?= ucfirst($user['role']) ?></span>
                </p>
                <p><strong>Status:</strong> 
                    <span class="badge <?= $user['is_active'] ? 'bg-success' : 'bg-secondary' ?>">
                        <?= $user['is_active'] ? 'Aktif' : 'Nonaktif' ?>
                    </span>
                </p>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Petunjuk</h5>
            </div>
            <div class="card-body">
                <ul class="small">
                    <li>Pilih kelas yang dapat diakses oleh wali kelas ini</li>
                    <li>Satu wali kelas dapat mengajar beberapa kelas</li>
                    <li>Assignment berlaku per tahun pelajaran</li>
                    <li>Wali kelas hanya dapat melihat data siswa dari kelas yang di-assign</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
