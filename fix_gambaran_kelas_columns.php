<?php
// fix_gambaran_kelas_columns.php - Cek dan tambah kolom untuk gambaran kelas

include 'includes/db.php';

echo "<h2>🔍 Cek dan Perbaiki Kolom Gambaran Kelas</h2>";

// Cek struktur tabel kelas
$result = $conn->query("DESCRIBE kelas");
$columns = [];

echo "<h3>Struktur Tabel Kelas Saat Ini:</h3>";
echo "<table border='1' style='border-collapse: collapse; margin-bottom: 20px;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";

while ($row = $result->fetch_assoc()) {
    $columns[] = $row['Field'];
    echo "<tr>";
    echo "<td>" . $row['Field'] . "</td>";
    echo "<td>" . $row['Type'] . "</td>";
    echo "<td>" . $row['Null'] . "</td>";
    echo "<td>" . $row['Key'] . "</td>";
    echo "<td>" . $row['Default'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Kolom yang diperlukan untuk gambaran kelas
$required_columns = [
    'hasil_komitmen_bersama' => 'TEXT',
    'aspek_sanitas' => 'TEXT',
    'aspek_sactitas' => 'TEXT',
    'aspek_scientia' => 'TEXT',
    'mapel_agama' => 'TEXT',
    'mapel_bahasa_indonesia' => 'TEXT',
    'mapel_bahasa_inggris' => 'TEXT',
    'mapel_bahasa_latin' => 'TEXT',
    'mapel_lain' => 'TEXT',
    'created_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
    'updated_at' => 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
];

echo "<h3>Status Kolom yang Diperlukan:</h3>";
$missing_columns = [];

foreach ($required_columns as $column => $type) {
    if (in_array($column, $columns)) {
        echo "<div style='color: green;'>✅ Kolom '$column' sudah ada</div>";
    } else {
        echo "<div style='color: red;'>❌ Kolom '$column' BELUM ada</div>";
        $missing_columns[$column] = $type;
    }
}

// Tambah kolom yang hilang
if (!empty($missing_columns)) {
    echo "<h3>🔧 Menambahkan Kolom yang Hilang:</h3>";
    
    foreach ($missing_columns as $column => $type) {
        $sql = "ALTER TABLE kelas ADD COLUMN $column $type AFTER tingkat";
        
        if ($conn->query($sql)) {
            echo "<div style='color: green;'>✅ Kolom '$column' berhasil ditambahkan</div>";
        } else {
            echo "<div style='color: red;'>❌ Error menambah kolom '$column': " . $conn->error . "</div>";
        }
    }
    
    // Update existing records dengan nilai default kosong
    echo "<h3>🔄 Update Data Existing:</h3>";
    $update_fields = [];
    foreach ($missing_columns as $column => $type) {
        $update_fields[] = "$column = ''";
    }
    
    if (!empty($update_fields)) {
        $update_sql = "UPDATE kelas SET " . implode(', ', $update_fields) . " WHERE " . array_keys($missing_columns)[0] . " IS NULL";
        
        if ($conn->query($update_sql)) {
            echo "<div style='color: green;'>✅ Data existing berhasil diupdate</div>";
        } else {
            echo "<div style='color: red;'>❌ Error update data: " . $conn->error . "</div>";
        }
    }
    
} else {
    echo "<div style='color: green; font-weight: bold;'>🎉 Semua kolom sudah lengkap!</div>";
}

// Test query gambaran kelas
echo "<h3>🧪 Test Query Gambaran Kelas:</h3>";
$test_sql = "SELECT id_kelas, nama_kelas, hasil_komitmen_bersama, aspek_sanitas, aspek_sactitas, aspek_scientia, mapel_agama FROM kelas LIMIT 1";
$result = $conn->query($test_sql);

if ($result) {
    echo "<div style='color: green;'>✅ Query test berhasil</div>";
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo "<p><strong>Sample data:</strong></p>";
        echo "<ul>";
        foreach ($row as $key => $value) {
            echo "<li><strong>$key:</strong> " . ($value ?: 'NULL') . "</li>";
        }
        echo "</ul>";
    }
} else {
    echo "<div style='color: red;'>❌ Query test gagal: " . $conn->error . "</div>";
}

echo "<br><br>";
echo "<a href='guru/gambaran_kelas.php?kelas=1&tahun=1'>🔗 Test Gambaran Kelas</a> | ";
echo "<a href='login.php'>🔗 Login</a>";
?>
