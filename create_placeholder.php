<?php
// create_placeholder.php - Script untuk membuat placeholder.jpg

// Buat image 200x200 dengan background abu-abu
$width = 200;
$height = 200;

$image = imagecreatetruecolor($width, $height);

// Warna background abu-abu
$bg_color = imagecolorallocate($image, 200, 200, 200);
$text_color = imagecolorallocate($image, 100, 100, 100);

// Fill background
imagefill($image, 0, 0, $bg_color);

// Tambahkan teks "No Photo"
$font_size = 5;
$text = "No Photo";
$text_width = imagefontwidth($font_size) * strlen($text);
$text_height = imagefontheight($font_size);

$x = ($width - $text_width) / 2;
$y = ($height - $text_height) / 2;

imagestring($image, $font_size, $x, $y, $text, $text_color);

// Simpan sebagai JPEG
imagejpeg($image, 'uploads/placeholder.jpg', 80);

// Bersihkan memory
imagedestroy($image);

echo "✅ Placeholder image berhasil dibuat di uploads/placeholder.jpg";
?>
