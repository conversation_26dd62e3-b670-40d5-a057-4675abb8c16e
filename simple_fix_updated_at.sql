-- SIMPLE FIX - <PERSON><PERSON> tambah kolom updated_at dan created_at
-- Jalankan ini jika kolom gambaran kelas sudah ada

-- <PERSON><PERSON> kolom created_at jika belum ada
ALTER TABLE kelas ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- <PERSON><PERSON> kolom updated_at jika belum ada  
ALTER TABLE kelas ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;

-- Update existing records
UPDATE kelas SET 
    created_at = COALESCE(created_at, CURRENT_TIMESTAMP),
    updated_at = COALESCE(updated_at, CURRENT_TIMESTAMP);

-- <PERSON><PERSON> hasil
SELECT 'Kolom timestamp berhasil ditambahkan!' as status;
DESCRIBE kelas;
