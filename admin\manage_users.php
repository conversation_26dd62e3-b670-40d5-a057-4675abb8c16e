<?php
// admin/manage_users.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Akses ditolak. Halaman ini hanya untuk administrator.</div>');
}

$message = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_user':
                $username = trim($_POST['username']);
                $password = $_POST['password'];
                $nama_lengkap = trim($_POST['nama_lengkap']);
                $email = trim($_POST['email']);
                $role = $_POST['role'];
                
                // Validasi
                if (empty($username) || empty($password) || empty($nama_lengkap)) {
                    $message = '<div class="alert alert-danger">Username, password, dan nama lengkap harus diisi.</div>';
                } else {
                    // Cek username sudah ada
                    $stmt = $conn->prepare("SELECT id_user FROM users WHERE username = ?");
                    $stmt->bind_param("s", $username);
                    $stmt->execute();
                    
                    if ($stmt->get_result()->num_rows > 0) {
                        $message = '<div class="alert alert-danger">Username sudah digunakan.</div>';
                    } else {
                        // Hash password
                        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                        
                        // Insert user
                        $stmt = $conn->prepare("INSERT INTO users (username, password, nama_lengkap, email, role) VALUES (?, ?, ?, ?, ?)");
                        $stmt->bind_param("sssss", $username, $hashed_password, $nama_lengkap, $email, $role);
                        
                        if ($stmt->execute()) {
                            $message = '<div class="alert alert-success">User berhasil ditambahkan.</div>';
                        } else {
                            $message = '<div class="alert alert-danger">Gagal menambahkan user.</div>';
                        }
                    }
                }
                break;

            case 'edit_user':
                $id_user = $_POST['id_user'];
                $username = trim($_POST['username']);
                $nama_lengkap = trim($_POST['nama_lengkap']);
                $email = trim($_POST['email']);
                $role = $_POST['role'];
                $password = $_POST['password'] ?? '';

                // Validasi
                if (empty($username) || empty($nama_lengkap)) {
                    $message = '<div class="alert alert-danger">Username dan nama lengkap harus diisi.</div>';
                } else {
                    // Cek username sudah ada (kecuali untuk user yang sedang diedit)
                    $stmt = $conn->prepare("SELECT id_user FROM users WHERE username = ? AND id_user != ?");
                    $stmt->bind_param("si", $username, $id_user);
                    $stmt->execute();

                    if ($stmt->get_result()->num_rows > 0) {
                        $message = '<div class="alert alert-danger">Username sudah digunakan oleh user lain.</div>';
                    } else {
                        // Update user
                        if (!empty($password)) {
                            // Update dengan password baru
                            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                            $stmt = $conn->prepare("UPDATE users SET username = ?, password = ?, nama_lengkap = ?, email = ?, role = ? WHERE id_user = ?");
                            $stmt->bind_param("sssssi", $username, $hashed_password, $nama_lengkap, $email, $role, $id_user);
                        } else {
                            // Update tanpa mengubah password
                            $stmt = $conn->prepare("UPDATE users SET username = ?, nama_lengkap = ?, email = ?, role = ? WHERE id_user = ?");
                            $stmt->bind_param("ssssi", $username, $nama_lengkap, $email, $role, $id_user);
                        }

                        if ($stmt->execute()) {
                            $message = '<div class="alert alert-success">User berhasil diupdate.</div>';
                        } else {
                            $message = '<div class="alert alert-danger">Gagal mengupdate user: ' . $conn->error . '</div>';
                        }
                    }
                }
                break;

            case 'toggle_status':
                $id_user = $_POST['id_user'];
                $new_status = $_POST['new_status'];
                
                $stmt = $conn->prepare("UPDATE users SET is_active = ? WHERE id_user = ?");
                $stmt->bind_param("ii", $new_status, $id_user);
                
                if ($stmt->execute()) {
                    $status_text = $new_status ? 'diaktifkan' : 'dinonaktifkan';
                    $message = '<div class="alert alert-success">User berhasil ' . $status_text . '.</div>';
                } else {
                    $message = '<div class="alert alert-danger">Gagal mengubah status user.</div>';
                }
                break;
        }
    }
}

// Ambil data users
$stmt = $conn->prepare("
    SELECT u.*, 
           GROUP_CONCAT(CONCAT(k.nama_kelas, ' (', tp.tahun_ajaran, ')') SEPARATOR ', ') as kelas_list
    FROM users u
    LEFT JOIN user_kelas uk ON u.id_user = uk.id_user
    LEFT JOIN kelas k ON uk.id_kelas = k.id_kelas
    LEFT JOIN tahun_pelajaran tp ON uk.id_tahun = tp.id_tahun
    GROUP BY u.id_user
    ORDER BY u.role, u.nama_lengkap
");
$stmt->execute();
$users = $stmt->get_result();
?>

<!-- Modern Light Header -->
<div class="bg-gradient-primary-to-secondary py-4 mb-5 rounded-3">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="text-white fw-bold mb-2">
                    <i class="fas fa-users-cog me-3"></i>Kelola Users
                </h1>
                <p class="text-white-50 mb-0 fs-5">
                    Manajemen pengguna sistem Cura Personalis
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <div class="d-flex flex-column flex-lg-row gap-2 align-items-lg-end">
                    <button class="btn btn-light btn-lg" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-plus me-2"></i>Tambah User
                    </button>
                    <a href="dashboard.php" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $message ?>

<!-- Modern Users Table -->
<div class="card border-0 shadow-lg">
    <div class="card-header bg-white border-0 py-3">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0 text-dark fw-semibold">
                <i class="fas fa-table me-2 text-primary"></i>Daftar Users
            </h5>
            <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2">
                Total: <?= $users->num_rows ?> users
            </span>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                    <tr class="text-white">
                        <th class="border-0 fw-semibold">Username</th>
                        <th class="border-0 fw-semibold">Nama Lengkap</th>
                        <th class="border-0 fw-semibold">Email</th>
                        <th class="border-0 fw-semibold">Role</th>
                        <th class="border-0 fw-semibold">Kelas yang Diajar</th>
                        <th class="border-0 fw-semibold">Status</th>
                        <th class="border-0 fw-semibold">Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while($user = $users->fetch_assoc()): ?>
                        <tr>
                            <td class="align-middle">
                                <div class="fw-semibold text-dark"><?= htmlspecialchars($user['username']) ?></div>
                            </td>
                            <td class="align-middle">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                        <i class="fas fa-user text-primary"></i>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-dark"><?= htmlspecialchars($user['nama_lengkap']) ?></div>
                                    </div>
                                </div>
                            </td>
                            <td class="align-middle text-muted"><?= htmlspecialchars($user['email'] ?: '-') ?></td>
                            <td class="align-middle">
                                <span class="badge rounded-pill <?= $user['role'] === 'admin' ? 'bg-danger bg-opacity-10 text-danger' : 'bg-primary bg-opacity-10 text-primary' ?>">
                                    <i class="fas <?= $user['role'] === 'admin' ? 'fa-crown' : 'fa-chalkboard-teacher' ?> me-1"></i>
                                    <?= $user['role'] === 'admin' ? 'Administrator' : 'Wali Kelas' ?>
                                </span>
                            </td>
                            <td class="align-middle">
                                <?php if ($user['kelas_list']): ?>
                                    <small class="text-muted"><?= htmlspecialchars($user['kelas_list']) ?></small>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td class="align-middle">
                                <span class="badge rounded-pill <?= $user['is_active'] ? 'bg-success bg-opacity-10 text-success' : 'bg-secondary bg-opacity-10 text-secondary' ?>">
                                    <i class="fas <?= $user['is_active'] ? 'fa-check-circle' : 'fa-pause-circle' ?> me-1"></i>
                                    <?= $user['is_active'] ? 'Aktif' : 'Nonaktif' ?>
                                </span>
                            </td>
                            <td class="align-middle">
                                <div class="d-flex gap-1">
                                    <!-- Tombol Edit -->
                                    <button class="btn btn-sm btn-outline-primary rounded-pill"
                                            onclick="editUser(<?= $user['id_user'] ?>, '<?= htmlspecialchars($user['username']) ?>', '<?= htmlspecialchars($user['nama_lengkap']) ?>', '<?= htmlspecialchars($user['email']) ?>', '<?= $user['role'] ?>')"
                                            data-bs-toggle="modal" data-bs-target="#editUserModal"
                                            title="Edit User">
                                        <i class="fas fa-edit"></i>
                                    </button>

                                    <?php if ($user['id_user'] != $_SESSION['id_user']): ?>
                                        <!-- Toggle Status -->
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="id_user" value="<?= $user['id_user'] ?>">
                                            <input type="hidden" name="new_status" value="<?= $user['is_active'] ? 0 : 1 ?>">
                                            <button type="submit" class="btn btn-sm <?= $user['is_active'] ? 'btn-outline-warning' : 'btn-outline-success' ?> rounded-pill"
                                                    onclick="return confirm('Yakin ingin mengubah status user ini?')"
                                                    title="<?= $user['is_active'] ? 'Nonaktifkan' : 'Aktifkan' ?> User">
                                                <i class="fas <?= $user['is_active'] ? 'fa-pause' : 'fa-play' ?>"></i>
                                            </button>
                                        </form>

                                        <?php if ($user['role'] === 'wali_kelas'): ?>
                                            <a href="assign_kelas.php?user=<?= $user['id_user'] ?>"
                                               class="btn btn-sm btn-outline-info rounded-pill" title="Atur Kelas">
                                                <i class="fas fa-school"></i>
                                            </a>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <span class="badge bg-info bg-opacity-10 text-info rounded-pill">
                                            <i class="fas fa-user-check me-1"></i>Current User
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Tambah User -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title"><i class="fas fa-user-plus me-2"></i>Tambah User Baru</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_user">

                    <div class="mb-3">
                        <label class="form-label">Username *</label>
                        <input type="text" name="username" class="form-control" required>
                        <small class="text-muted">Username harus unik</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Password *</label>
                        <input type="password" name="password" class="form-control" required minlength="6">
                        <small class="text-muted">Minimal 6 karakter</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Nama Lengkap *</label>
                        <input type="text" name="nama_lengkap" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" class="form-control">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Role *</label>
                        <select name="role" class="form-control" required>
                            <option value="wali_kelas">Wali Kelas</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Tambah User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit User -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title"><i class="fas fa-user-edit me-2"></i>Edit User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_user">
                    <input type="hidden" name="id_user" id="edit_id_user">

                    <div class="mb-3">
                        <label class="form-label">Username *</label>
                        <input type="text" name="username" id="edit_username" class="form-control" required>
                        <small class="text-muted">Username harus unik</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Password</label>
                        <input type="password" name="password" id="edit_password" class="form-control" minlength="6">
                        <small class="text-muted">Kosongkan jika tidak ingin mengubah password</small>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Nama Lengkap *</label>
                        <input type="text" name="nama_lengkap" id="edit_nama_lengkap" class="form-control" required>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" name="email" id="edit_email" class="form-control">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Role *</label>
                        <select name="role" id="edit_role" class="form-control" required>
                            <option value="wali_kelas">Wali Kelas</option>
                            <option value="admin">Administrator</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-2"></i>Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Function untuk mengisi form edit user
function editUser(id, username, nama_lengkap, email, role) {
    document.getElementById('edit_id_user').value = id;
    document.getElementById('edit_username').value = username;
    document.getElementById('edit_nama_lengkap').value = nama_lengkap;
    document.getElementById('edit_email').value = email || '';
    document.getElementById('edit_role').value = role;
    document.getElementById('edit_password').value = ''; // Reset password field
}

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            if (alert.classList.contains('show')) {
                alert.classList.remove('show');
                alert.classList.add('fade');
                setTimeout(function() {
                    alert.remove();
                }, 150);
            }
        }, 5000);
    });
});

// Form validation
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            const passwordField = form.querySelector('input[name="password"]');
            const actionField = form.querySelector('input[name="action"]');

            // Validasi password untuk add user
            if (actionField && actionField.value === 'add_user') {
                if (passwordField && passwordField.value.length < 6) {
                    event.preventDefault();
                    alert('Password minimal 6 karakter');
                    return false;
                }
            }

            // Validasi password untuk edit user (jika diisi)
            if (actionField && actionField.value === 'edit_user') {
                if (passwordField && passwordField.value.length > 0 && passwordField.value.length < 6) {
                    event.preventDefault();
                    alert('Password minimal 6 karakter');
                    return false;
                }
            }
        });
    });
});
</script>

<?php include '../includes/footer.php'; ?>
