<?php
// admin/dashboard.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Akses ditolak. Halaman ini hanya untuk administrator.</div>');
}

// Statistik dashboard
$stats = [];

// Total users
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM users WHERE is_active = 1");
$stmt->execute();
$stats['total_users'] = $stmt->get_result()->fetch_assoc()['total'];

// Total wali kelas
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM users WHERE role = 'wali_kelas' AND is_active = 1");
$stmt->execute();
$stats['total_wali'] = $stmt->get_result()->fetch_assoc()['total'];

// Total kelas
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM kelas");
$stmt->execute();
$stats['total_kelas'] = $stmt->get_result()->fetch_assoc()['total'];

// Total siswa tahun aktif
$tahun_aktif = getActiveTahun($conn);
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM siswa WHERE id_tahun = ?");
$stmt->bind_param("i", $tahun_aktif['id_tahun']);
$stmt->execute();
$stats['total_siswa'] = $stmt->get_result()->fetch_assoc()['total'];

// Kelas tanpa wali
$stmt = $conn->prepare("
    SELECT k.id_kelas, k.nama_kelas 
    FROM kelas k 
    LEFT JOIN user_kelas uk ON k.id_kelas = uk.id_kelas AND uk.id_tahun = ?
    WHERE uk.id_kelas IS NULL
    ORDER BY k.nama_kelas
");
$stmt->bind_param("i", $tahun_aktif['id_tahun']);
$stmt->execute();
$kelas_tanpa_wali = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="fas fa-tachometer-alt"></i> Dashboard Administrator</h3>
    <div>
        <span class="badge bg-primary">Tahun Aktif: <?= htmlspecialchars($tahun_aktif['tahun_ajaran']) ?></span>
    </div>
</div>

<!-- Enhanced Statistics Cards -->
<div class="row mb-5 g-4">
    <div class="col-md-3">
        <div class="stats-card stats-primary fade-in-up stagger-1">
            <div class="stats-icon icon-primary">
                <i class="fas fa-users"></i>
            </div>
            <div class="stats-number text-primary"><?= $stats['total_users'] ?></div>
            <div class="stats-label">Total Users</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card stats-success fade-in-up stagger-2">
            <div class="stats-icon icon-success">
                <i class="fas fa-chalkboard-teacher"></i>
            </div>
            <div class="stats-number text-success"><?= $stats['total_wali'] ?></div>
            <div class="stats-label">Wali Kelas</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card stats-info fade-in-up stagger-3">
            <div class="stats-icon icon-info">
                <i class="fas fa-school"></i>
            </div>
            <div class="stats-number text-info"><?= $stats['total_kelas'] ?></div>
            <div class="stats-label">Total Kelas</div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="stats-card stats-warning fade-in-up stagger-4">
            <div class="stats-icon icon-warning">
                <i class="fas fa-user-graduate"></i>
            </div>
            <div class="stats-number text-warning"><?= $stats['total_siswa'] ?></div>
            <div class="stats-label">Total Siswa</div>
        </div>
    </div>
</div>

<!-- Enhanced Quick Actions -->
<div class="row mb-5">
    <div class="col-md-12">
        <div class="card card-modern fade-in-up stagger-5">
            <div class="card-header bg-gradient-primary text-white">
                <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body p-4">
                <div class="row g-3">
                    <div class="col-md-3">
                        <a href="manage_users.php" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-users fa-2x mb-2"></i>
                            <span>Kelola Users</span>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="manage_kelas.php" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-school fa-2x mb-2"></i>
                            <span>Kelola Kelas</span>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="manage_tahun.php" class="btn btn-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-calendar fa-2x mb-2"></i>
                            <span>Tahun Pelajaran</span>
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="view_all_kelas.php" class="btn btn-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <span>Akses Semua Kelas</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Kelas Tanpa Wali -->
<?php if ($kelas_tanpa_wali->num_rows > 0): ?>
<div class="row">
    <div class="col-md-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-exclamation-triangle"></i> Kelas Tanpa Wali (Tahun <?= htmlspecialchars($tahun_aktif['tahun_ajaran']) ?>)</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 
                    Kelas-kelas berikut belum memiliki wali kelas untuk tahun pelajaran aktif.
                </div>
                <div class="row">
                    <?php while($kelas = $kelas_tanpa_wali->fetch_assoc()): ?>
                        <div class="col-md-4 mb-2">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h6><?= htmlspecialchars($kelas['nama_kelas']) ?></h6>
                                    <a href="assign_wali.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= $tahun_aktif['id_tahun'] ?>" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus"></i> Assign Wali
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php endwhile; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include '../includes/footer.php'; ?>
