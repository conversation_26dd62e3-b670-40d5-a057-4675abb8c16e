<?php
// guru/detail_siswa_list.php - Daftar detail siswa dalam kelas

include '../includes/header.php';

$id_kelas = $_GET['kelas'] ?? 0;
$id_tahun = $_GET['tahun'] ?? 0;

// Validasi parameter dan akses
if (!$id_kelas || !$id_tahun) {
    die('<div class="alert alert-danger">Parameter kelas dan tahun harus diisi.</div>');
}

// Cek akses kelas
if (isset($_SESSION['id_user'])) {
    requireKelasAccess($conn, $id_kelas, $id_tahun);
} else {
    die('<div class="alert alert-danger">Session tidak valid. Silakan login ulang.</div>');
}

// Get info kelas dan tahun
$stmt = $conn->prepare("
    SELECT k.nama_kelas, k.tingkat, tp.tahun_ajaran 
    FROM kelas k 
    JOIN tahun_pelajaran tp ON tp.id_tahun = ?
    WHERE k.id_kelas = ?
");
$stmt->bind_param("ii", $id_tahun, $id_kelas);
$stmt->execute();
$info = $stmt->get_result()->fetch_assoc();

// Get daftar siswa
$stmt = $conn->prepare("
    SELECT s.*, 
           CASE WHEN s.foto IS NULL OR s.foto = '' THEN 'placeholder.jpg' ELSE s.foto END as foto_display
    FROM siswa s 
    WHERE s.id_kelas = ? AND s.id_tahun = ?
    ORDER BY s.nama_lengkap
");
$stmt->bind_param("ii", $id_kelas, $id_tahun);
$stmt->execute();
$siswa_list = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h3><i class="fas fa-users"></i> Daftar Siswa - <?= htmlspecialchars($info['nama_kelas']) ?></h3>
        <p class="text-muted mb-0">
            Tingkat: <?= htmlspecialchars($info['tingkat']) ?> | 
            Tahun: <?= htmlspecialchars($info['tahun_ajaran']) ?>
        </p>
    </div>
    <div>
        <a href="gambaran_kelas.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Gambaran
        </a>
        <?php if ($_SESSION['role'] === 'admin'): ?>
        <a href="../admin/add_siswa.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Siswa
        </a>
        <?php endif; ?>
    </div>
</div>

<?php if ($siswa_list->num_rows > 0): ?>
<div class="row">
    <?php while ($siswa = $siswa_list->fetch_assoc()): ?>
    <div class="col-md-6 col-lg-4 mb-4">
        <div class="card h-100">
            <div class="card-body">
                <div class="text-center mb-3">
                    <img src="../uploads/<?= htmlspecialchars($siswa['foto_display']) ?>" 
                         alt="Foto <?= htmlspecialchars($siswa['nama_lengkap']) ?>"
                         class="rounded-circle mb-2" style="width: 80px; height: 80px; object-fit: cover;"
                         onerror="this.src='../uploads/placeholder.jpg'">
                    
                    <h6 class="card-title mb-1"><?= htmlspecialchars($siswa['nama_lengkap']) ?></h6>
                    
                    <?php if ($siswa['nama_panggilan']): ?>
                    <small class="text-muted">"<?= htmlspecialchars($siswa['nama_panggilan']) ?>"</small>
                    <?php endif; ?>
                </div>

                <div class="mb-3">
                    <?php if ($siswa['is_potensial']): ?>
                        <span class="badge bg-success me-1">
                            <i class="fas fa-star"></i> Potensial
                        </span>
                    <?php endif; ?>
                    
                    <?php if ($siswa['is_catatan_khusus']): ?>
                        <span class="badge bg-warning">
                            <i class="fas fa-exclamation-triangle"></i> Catatan Khusus
                        </span>
                    <?php endif; ?>
                    
                    <span class="badge bg-<?= $siswa['status_ppdb'] == 'Pantas' ? 'success' : ($siswa['status_ppdb'] == 'Diterima' ? 'primary' : 'warning') ?>">
                        <?= htmlspecialchars($siswa['status_ppdb']) ?>
                    </span>
                </div>

                <div class="small text-muted mb-3">
                    <?php if ($siswa['asal_paroki']): ?>
                    <div><i class="fas fa-church"></i> <?= htmlspecialchars($siswa['asal_paroki']) ?></div>
                    <?php endif; ?>
                    
                    <?php if ($siswa['keuskupan']): ?>
                    <div><i class="fas fa-map-marker-alt"></i> <?= htmlspecialchars($siswa['keuskupan']) ?></div>
                    <?php endif; ?>
                </div>

                <!-- Preview Data Pendampingan -->
                <?php if ($siswa['gambaran_umum'] || $siswa['kondisi_saat_ini'] || $siswa['catatan'] || $siswa['catatan_lain'] || $siswa['catatan_kepamongan'] || $siswa['pendampingan']): ?>
                <div class="border-top pt-2">
                    <small class="text-muted">
                        <i class="fas fa-clipboard"></i> Ada data pendampingan
                    </small>
                </div>
                <?php endif; ?>

                <div class="d-grid gap-2 mt-3">
                    <a href="detail_siswa.php?id=<?= $siswa['id_siswa'] ?>&kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" 
                       class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> Lihat Detail
                    </a>
                    
                    <?php if ($_SESSION['role'] === 'admin'): ?>
                    <div class="btn-group btn-group-sm">
                        <a href="../admin/edit_siswa.php?id=<?= $siswa['id_siswa'] ?>&kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" 
                           class="btn btn-outline-warning">
                            <i class="fas fa-edit"></i> Edit
                        </a>
                        <button class="btn btn-outline-danger" 
                                onclick="confirmDelete(<?= $siswa['id_siswa'] ?>, '<?= htmlspecialchars($siswa['nama_lengkap']) ?>')">
                            <i class="fas fa-trash"></i> Hapus
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endwhile; ?>
</div>

<!-- Pagination jika diperlukan -->
<div class="d-flex justify-content-center mt-4">
    <nav>
        <p class="text-muted">Total: <?= $siswa_list->num_rows ?> siswa</p>
    </nav>
</div>

<?php else: ?>
<div class="text-center py-5">
    <i class="fas fa-users fa-3x text-muted mb-3"></i>
    <h5 class="text-muted">Belum Ada Siswa</h5>
    <p class="text-muted">Kelas ini belum memiliki siswa untuk tahun pelajaran <?= htmlspecialchars($info['tahun_ajaran']) ?></p>
    
    <?php if ($_SESSION['role'] === 'admin'): ?>
    <a href="../admin/add_siswa.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> Tambah Siswa Pertama
    </a>
    <?php endif; ?>
</div>
<?php endif; ?>

<!-- Filter dan Search (untuk pengembangan future) -->
<div class="position-fixed bottom-0 end-0 p-3">
    <div class="btn-group-vertical">
        <button class="btn btn-info btn-sm mb-2" data-bs-toggle="modal" data-bs-target="#filterModal">
            <i class="fas fa-filter"></i>
        </button>
        <a href="gambaran_kelas.php?kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>" class="btn btn-secondary btn-sm">
            <i class="fas fa-chart-line"></i>
        </a>
    </div>
</div>

<!-- Modal Filter -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Filter Siswa</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Status PPDB</label>
                    <select class="form-select" id="filterPPDB">
                        <option value="">Semua Status</option>
                        <option value="Pantas">Pantas</option>
                        <option value="Diterima">Diterima</option>
                        <option value="Dicoba">Dicoba</option>
                    </select>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="filterPotensial">
                        <label class="form-check-label" for="filterPotensial">
                            Hanya Siswa Potensial
                        </label>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="filterCatatanKhusus">
                        <label class="form-check-label" for="filterCatatanKhusus">
                            Hanya Catatan Khusus
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
                <button type="button" class="btn btn-primary" onclick="applyFilter()">Terapkan Filter</button>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id, nama) {
    if (confirm(`Apakah Anda yakin ingin menghapus siswa "${nama}"?\n\nTindakan ini tidak dapat dibatalkan.`)) {
        window.location.href = `../admin/delete_siswa.php?id=${id}&kelas=<?= $id_kelas ?>&tahun=<?= $id_tahun ?>`;
    }
}

function applyFilter() {
    // Implementasi filter untuk future development
    alert('Fitur filter akan dikembangkan lebih lanjut');
    $('#filterModal').modal('hide');
}
</script>

<?php include '../includes/footer.php'; ?>
