-- Update database untuk menambah field baru di gambaran kelas
-- Jalankan script ini di phpMyAdmin atau MySQL

-- Tambah field baru di tabel kelas untuk gambaran kelas
ALTER TABLE kelas ADD COLUMN hasil_komitmen_bersama TEXT AFTER tingkat;
ALTER TABLE kelas ADD COLUMN aspek_sanitas TEXT AFTER hasil_komitmen_bersama;
ALTER TABLE kelas ADD COLUMN aspek_sactitas TEXT AFTER aspek_sanitas;
ALTER TABLE kelas ADD COLUMN aspek_scientia TEXT AFTER aspek_sactitas;

-- Tambah field untuk catatan kenaikan mapel prasyarat
ALTER TABLE kelas ADD COLUMN mapel_agama TEXT AFTER aspek_scientia;
ALTER TABLE kelas ADD COLUMN mapel_bahasa_indonesia TEXT AFTER mapel_agama;
ALTER TABLE kelas ADD COLUMN mapel_bahasa_inggris TEXT AFTER mapel_bahasa_indonesia;
ALTER TABLE kelas ADD COLUMN mapel_bahasa_latin TEXT AFTER mapel_bahasa_inggris;

-- Tambah field untuk mapel lain
ALTER TABLE kelas ADD COLUMN mapel_lain TEXT AFTER mapel_bahasa_latin;

-- Update existing records dengan nilai default kosong
UPDATE kelas SET 
    hasil_komitmen_bersama = '',
    aspek_sanitas = '',
    aspek_sactitas = '',
    aspek_scientia = '',
    mapel_agama = '',
    mapel_bahasa_indonesia = '',
    mapel_bahasa_inggris = '',
    mapel_bahasa_latin = '',
    mapel_lain = ''
WHERE hasil_komitmen_bersama IS NULL;
