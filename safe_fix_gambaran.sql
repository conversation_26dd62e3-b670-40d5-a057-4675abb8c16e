-- SAFE FIX untuk G<PERSON>bara<PERSON> - Tidak akan error jika kolom sudah ada
-- Copy paste ke phpMyAdmin dan jalankan

-- Cek dan tambah kolom hasil_komitmen_bersama
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'hasil_komitmen_bersama') > 0,
    "SELECT 'Kolom hasil_komitmen_bersama sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN hasil_komitmen_bersama TEXT"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom aspek_sanitas
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'aspek_sanitas') > 0,
    "SELECT 'Kolom aspek_sanitas sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN aspek_sanitas TEXT"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom aspek_sactitas
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'aspek_sactitas') > 0,
    "SELECT 'Kolom aspek_sactitas sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN aspek_sactitas TEXT"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom aspek_scientia
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'aspek_scientia') > 0,
    "SELECT 'Kolom aspek_scientia sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN aspek_scientia TEXT"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_agama
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'mapel_agama') > 0,
    "SELECT 'Kolom mapel_agama sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN mapel_agama TEXT"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_bahasa_indonesia
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'mapel_bahasa_indonesia') > 0,
    "SELECT 'Kolom mapel_bahasa_indonesia sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN mapel_bahasa_indonesia TEXT"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_bahasa_inggris
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'mapel_bahasa_inggris') > 0,
    "SELECT 'Kolom mapel_bahasa_inggris sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN mapel_bahasa_inggris TEXT"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_bahasa_latin
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'mapel_bahasa_latin') > 0,
    "SELECT 'Kolom mapel_bahasa_latin sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN mapel_bahasa_latin TEXT"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom mapel_lain
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'mapel_lain') > 0,
    "SELECT 'Kolom mapel_lain sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN mapel_lain TEXT"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom created_at
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'created_at') > 0,
    "SELECT 'Kolom created_at sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Cek dan tambah kolom updated_at
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE table_name = 'kelas' 
     AND table_schema = DATABASE() 
     AND column_name = 'updated_at') > 0,
    "SELECT 'Kolom updated_at sudah ada' as message",
    "ALTER TABLE kelas ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing records dengan nilai default kosong untuk kolom TEXT
UPDATE kelas SET 
    hasil_komitmen_bersama = COALESCE(hasil_komitmen_bersama, ''),
    aspek_sanitas = COALESCE(aspek_sanitas, ''),
    aspek_sactitas = COALESCE(aspek_sactitas, ''),
    aspek_scientia = COALESCE(aspek_scientia, ''),
    mapel_agama = COALESCE(mapel_agama, ''),
    mapel_bahasa_indonesia = COALESCE(mapel_bahasa_indonesia, ''),
    mapel_bahasa_inggris = COALESCE(mapel_bahasa_inggris, ''),
    mapel_bahasa_latin = COALESCE(mapel_bahasa_latin, ''),
    mapel_lain = COALESCE(mapel_lain, '')
WHERE hasil_komitmen_bersama IS NULL 
   OR aspek_sanitas IS NULL 
   OR aspek_sactitas IS NULL 
   OR aspek_scientia IS NULL 
   OR mapel_agama IS NULL 
   OR mapel_bahasa_indonesia IS NULL 
   OR mapel_bahasa_inggris IS NULL 
   OR mapel_bahasa_latin IS NULL 
   OR mapel_lain IS NULL;

-- Tampilkan hasil akhir
SELECT 'SAFE FIX COMPLETED - Gambaran kelas ready!' as status;
SELECT COUNT(*) as total_kelas FROM kelas;

-- Tampilkan struktur tabel untuk verifikasi
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'kelas' 
  AND TABLE_SCHEMA = DATABASE()
  AND COLUMN_NAME IN (
    'hasil_komitmen_bersama', 'aspek_sanitas', 'aspek_sactitas', 'aspek_scientia',
    'mapel_agama', 'mapel_bahasa_indonesia', 'mapel_bahasa_inggris', 
    'mapel_bahasa_latin', 'mapel_lain', 'created_at', 'updated_at'
  )
ORDER BY ORDINAL_POSITION;
