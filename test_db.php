<?php
// test_db.php - File untuk test koneksi database

echo "<h2>Test Koneksi Database</h2>";

// Test koneksi database
include 'includes/db.php';

if ($conn->connect_error) {
    echo "<div style='color: red;'>❌ Koneksi database GAGAL: " . $conn->connect_error . "</div>";
} else {
    echo "<div style='color: green;'>✅ Koneksi database BERHASIL</div>";
    
    // Test tabel users
    $result = $conn->query("SELECT COUNT(*) as count FROM users");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<div style='color: blue;'>📊 Total users: " . $row['count'] . "</div>";
    } else {
        echo "<div style='color: red;'>❌ Tabel users tidak ditemukan</div>";
    }
    
    // Test tabel kelas
    $result = $conn->query("SELECT COUNT(*) as count FROM kelas");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<div style='color: blue;'>📊 Total kelas: " . $row['count'] . "</div>";
    } else {
        echo "<div style='color: red;'>❌ Tabel kelas tidak ditemukan</div>";
    }
    
    // Test tabel tahun_pelajaran
    $result = $conn->query("SELECT COUNT(*) as count FROM tahun_pelajaran");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<div style='color: blue;'>📊 Total tahun pelajaran: " . $row['count'] . "</div>";
    } else {
        echo "<div style='color: red;'>❌ Tabel tahun_pelajaran tidak ditemukan</div>";
    }
}

echo "<br><a href='login.php'>🔗 Ke Halaman Login</a>";
?>
