# Fix Gambaran <PERSON>las Errors

## 🚨 **Errors yang <PERSON><PERSON>iki**

### **1. Undefined Variable Error**
```
Notice: Undefined variable: has_gambaran_data in C:\xampp2\htdocs\cura_personalis\guru\gambaran_kelas.php on line 82
```

### **2. Headers Already Sent Error**
```
Warning: Cannot modify header information - headers already sent by (output started at C:\xampp2\htdocs\cura_personalis\includes\header.php:105) in C:\xampp2\htdocs\cura_personalis\guru\gambaran_kelas.php on line 84
```

## 🔍 **Analisis Masalah**

### **Problem 1: Undefined Variable**
- **Penyebab**: Variabel `$has_gambaran_data` digunakan pada line 82 sebelum didefinisikan
- **Lokasi**: Variable didefinisikan pada line 99, tapi digunakan pada line 82
- **Impact**: PHP Notice dan logic error

### **Problem 2: Headers Already Sent**
- **Penyebab**: `header()` dipanggil setelah HTML output sudah dimulai dari `includes/header.php`
- **Lokasi**: `header("Location: ...")` pada line 84
- **Impact**: Redirect tidak berfungsi dan PHP Warning

## 🔧 **Perbaikan yang Dilakukan**

### **1. Fix Undefined Variable**

**Sebelum:**
```php
// Line 22-26: Form processing dimulai
if ($_POST && ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'wali_kelas')) {
    // ... processing ...
    
    // Line 82: Variable digunakan sebelum didefinisikan
    $success = "Data berhasil " . ($has_gambaran_data ? "diupdate" : "disimpan") . ".";
    
    // Line 92-99: Variable baru didefinisikan di sini
    $stmt = $conn->prepare("SELECT * FROM kelas WHERE id_kelas = ?");
    // ...
    $has_gambaran_data = !empty($kelas['hasil_komitmen_bersama']) || ...;
}
```

**Sesudah:**
```php
// Line 22-38: Ambil data kelas TERLEBIH DAHULU
$stmt = $conn->prepare("SELECT * FROM kelas WHERE id_kelas = ?");
$stmt->bind_param("i", $id_kelas);
$stmt->execute();
$kelas = $stmt->get_result()->fetch_assoc();

// Definisikan variable SEBELUM digunakan
$has_gambaran_data = !empty($kelas['hasil_komitmen_bersama']) ||
                     !empty($kelas['aspek_sanitas']) ||
                     // ... semua field lainnya

// Line 39-43: Baru kemudian form processing
if ($_POST && ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'wali_kelas')) {
    // ... processing ...
    
    // Line 98: Variable sudah terdefinisi, bisa digunakan
    $success = "Data berhasil " . ($has_gambaran_data ? "diupdate" : "disimpan") . ".";
}
```

### **2. Fix Headers Already Sent**

**Sebelum:**
```php
if ($stmt->execute()) {
    $success = "Data berhasil disimpan.";
    // ERROR: header() setelah HTML output
    header("Location: ?kelas=$id_kelas&tahun=$id_tahun&saved=1");
    exit();
}
```

**Sesudah:**
```php
if ($stmt->execute()) {
    $success = "Data berhasil disimpan.";
    
    // Refresh data kelas setelah update
    $stmt = $conn->prepare("SELECT * FROM kelas WHERE id_kelas = ?");
    $stmt->bind_param("i", $id_kelas);
    $stmt->execute();
    $kelas = $stmt->get_result()->fetch_assoc();
    
    // Update status has_gambaran_data
    $has_gambaran_data = !empty($kelas['hasil_komitmen_bersama']) || ...;
    
    // JavaScript redirect untuk menghindari headers already sent
    echo "<script>
        setTimeout(function() {
            window.location.href = '?kelas=$id_kelas&tahun=$id_tahun&saved=1';
        }, 1500);
    </script>";
}
```

## ✅ **Hasil Perbaikan**

### **Sebelum:**
- 🔴 **PHP Notice**: Undefined variable `$has_gambaran_data`
- 🔴 **PHP Warning**: Headers already sent
- 🔴 **Redirect tidak berfungsi** setelah save
- 🔴 **Logic error** pada pesan success/update

### **Sesudah:**
- ✅ **No PHP errors** atau warnings
- ✅ **Variable terdefinisi** sebelum digunakan
- ✅ **Redirect berfungsi** dengan JavaScript
- ✅ **Logic benar** untuk pesan update/simpan
- ✅ **Data refresh** setelah update
- ✅ **User experience** yang smooth dengan delay 1.5 detik

## 🧪 **Testing Scenarios**

### **1. Input Data Baru**
- ✅ Form muncul dengan label "Input Gambaran Kelas"
- ✅ Submit berhasil dengan pesan "Data berhasil disimpan"
- ✅ Auto redirect ke view mode setelah 1.5 detik
- ✅ No PHP errors

### **2. Update Data Existing**
- ✅ Form muncul dengan label "Edit Gambaran Kelas"
- ✅ Data existing ter-load di form
- ✅ Submit berhasil dengan pesan "Data berhasil diupdate"
- ✅ Auto redirect ke view mode setelah 1.5 detik
- ✅ No PHP errors

### **3. View Mode**
- ✅ Data gambaran kelas ditampilkan dengan baik
- ✅ Button "Edit" berfungsi
- ✅ No undefined variable errors

## 🔍 **Technical Details**

### **Variable Definition Order**
```php
// 1. Include header (HTML output dimulai)
include '../includes/header.php';

// 2. Get parameters
$id_kelas = $_GET['kelas'] ?? null;
$id_tahun = $_GET['tahun'] ?? null;

// 3. Ambil data kelas DULU
$stmt = $conn->prepare("SELECT * FROM kelas WHERE id_kelas = ?");
$kelas = $stmt->get_result()->fetch_assoc();

// 4. Definisikan variable DULU
$has_gambaran_data = !empty($kelas['hasil_komitmen_bersama']) || ...;

// 5. BARU form processing (yang menggunakan variable)
if ($_POST) {
    // Bisa menggunakan $has_gambaran_data dengan aman
}
```

### **Redirect Strategy**
```javascript
// JavaScript redirect dengan delay untuk UX yang lebih baik
setTimeout(function() {
    window.location.href = '?kelas=' + id_kelas + '&tahun=' + id_tahun + '&saved=1';
}, 1500); // 1.5 detik delay untuk user bisa baca pesan success
```

## 📝 **Files Modified**

1. **`guru/gambaran_kelas.php`**
   - Moved data fetching before form processing
   - Fixed variable definition order
   - Replaced `header()` redirect with JavaScript redirect
   - Added data refresh after update
   - Removed duplicate code

---

**Status**: ✅ **FIXED**  
**PHP Errors**: ✅ **RESOLVED**  
**Functionality**: ✅ **WORKING**  
**Ready for Use**: ✅ **YES**
