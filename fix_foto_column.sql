-- fix_foto_column.sql
-- Script untuk menambah kolom foto jika belum ada

-- <PERSON><PERSON> apakah kolom foto sudah ada
SET @col_exists = 0;
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
  AND table_name = 'siswa' 
  AND column_name = 'foto';

-- Tambah kolom foto jika belum ada
SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE siswa ADD COLUMN foto VARCHAR(255) DEFAULT ''placeholder.jpg'' AFTER hasil_pendampingan',
    'SELECT ''Kolom foto sudah ada'' as status'
);

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update existing records yang belum punya foto
UPDATE siswa 
SET foto = 'placeholder.jpg' 
WHERE foto IS NULL OR foto = '';

-- <PERSON><PERSON><PERSON><PERSON> hasil
SELECT 
    'Kolom foto berhasil ditambahkan/diupdate' as status,
    COUNT(*) as total_siswa,
    SUM(CASE WHEN foto = 'placeholder.jpg' THEN 1 ELSE 0 END) as siswa_placeholder,
    SUM(CASE WHEN foto != 'placeholder.jpg' THEN 1 ELSE 0 END) as siswa_dengan_foto
FROM siswa;
