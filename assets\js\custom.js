/**
 * Custom JavaScript for Cura Personalis
 * Additional functionality and fixes
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Fix text visibility issues
    fixTextVisibility();
    
    // Initialize additional features
    initAdditionalFeatures();
    
    // Fix button text visibility
    fixButtonTextVisibility();
    
    // Fix card text visibility
    fixCardTextVisibility();
});

/**
 * Fix text visibility issues
 */
function fixTextVisibility() {
    // Ensure all text elements have proper color
    const textElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div, a, label');
    
    textElements.forEach(element => {
        // Skip elements that already have explicit color styles
        if (!element.style.color && !element.classList.contains('text-white')) {
            const computedStyle = window.getComputedStyle(element);
            if (computedStyle.color === 'rgba(0, 0, 0, 0)' || computedStyle.color === 'transparent') {
                element.style.color = '#374151'; // Default gray-700
            }
        }
    });
}

/**
 * Fix button text visibility
 */
function fixButtonTextVisibility() {
    const buttons = document.querySelectorAll('.btn');
    
    buttons.forEach(button => {
        // Ensure button text and icons are visible
        const textElements = button.querySelectorAll('*');
        textElements.forEach(el => {
            if (button.classList.contains('btn-primary') || 
                button.classList.contains('btn-success') || 
                button.classList.contains('btn-warning') || 
                button.classList.contains('btn-info') || 
                button.classList.contains('btn-danger')) {
                el.style.color = 'white';
            }
        });
    });
}

/**
 * Fix card text visibility
 */
function fixCardTextVisibility() {
    const cards = document.querySelectorAll('.card, .stats-card, .class-card');
    
    cards.forEach(card => {
        // Fix card titles
        const titles = card.querySelectorAll('.card-title, .class-title, h1, h2, h3, h4, h5, h6');
        titles.forEach(title => {
            if (!title.style.color) {
                title.style.color = '#111827'; // gray-900
            }
        });
        
        // Fix card text
        const texts = card.querySelectorAll('.card-text, .class-subtitle, p, span');
        texts.forEach(text => {
            if (!text.style.color && !text.classList.contains('text-white')) {
                text.style.color = '#4b5563'; // gray-600
            }
        });
        
        // Fix stats numbers
        const statsNumbers = card.querySelectorAll('.stats-number');
        statsNumbers.forEach(num => {
            // Keep the color from CSS classes
            if (num.classList.contains('text-primary')) {
                num.style.color = '#667eea';
            } else if (num.classList.contains('text-success')) {
                num.style.color = '#10b981';
            } else if (num.classList.contains('text-warning')) {
                num.style.color = '#f59e0b';
            } else if (num.classList.contains('text-info')) {
                num.style.color = '#3b82f6';
            } else if (num.classList.contains('text-danger')) {
                num.style.color = '#ef4444';
            }
        });
        
        // Fix stats labels
        const statsLabels = card.querySelectorAll('.stats-label');
        statsLabels.forEach(label => {
            label.style.color = '#6b7280'; // gray-500
        });
    });
}

/**
 * Initialize additional features
 */
function initAdditionalFeatures() {
    // Add click feedback to cards
    const cards = document.querySelectorAll('.card, .stats-card, .class-card');
    cards.forEach(card => {
        card.addEventListener('click', function(e) {
            // Add visual feedback
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
    
    // Improve form interactions
    const formControls = document.querySelectorAll('.form-control, .form-select');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        control.addEventListener('blur', function() {
            if (!this.value) {
                this.parentElement.classList.remove('focused');
            }
        });
    });
    
    // Add loading states to forms
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.disabled) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                submitBtn.disabled = true;
                
                // Re-enable after 10 seconds as fallback
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 10000);
            }
        });
    });
}

/**
 * Utility function to show notifications
 */
function showNotification(message, type = 'info', duration = 5000) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 1050;
        min-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after duration
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, duration);
}

/**
 * Fix any remaining visibility issues on page load
 */
window.addEventListener('load', function() {
    setTimeout(() => {
        fixTextVisibility();
        fixButtonTextVisibility();
        fixCardTextVisibility();
    }, 100);
});

/**
 * Fix visibility issues when content is dynamically added
 */
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            setTimeout(() => {
                fixTextVisibility();
                fixButtonTextVisibility();
                fixCardTextVisibility();
            }, 50);
        }
    });
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});
