/* Modern Light Theme for Cura Personalis */

/* ===== GLOBAL STYLES ===== */
:root {
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --secondary-color: #764ba2;
    --success-color: #10b981;
    --success-dark: #059669;
    --warning-color: #f59e0b;
    --warning-dark: #d97706;
    --danger-color: #ef4444;
    --danger-dark: #dc2626;
    --info-color: #3b82f6;
    --info-dark: #2563eb;
    --light-color: #f8fafc;
    --dark-color: #1e293b;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --border-radius: 16px;
    --border-radius-lg: 20px;
    --border-radius-sm: 12px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    --gradient-success: linear-gradient(135deg, #10b981 0%, #059669 100%);
    --gradient-warning: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    --gradient-danger: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    --gradient-info: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #334155;
    line-height: 1.6;
    min-height: 100vh;
}

/* ===== MODERN CARDS ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: white;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.card:hover::before {
    opacity: 1;
}

.hover-lift {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

/* ===== ENHANCED CARD VARIANTS ===== */
.card-modern {
    border-radius: var(--border-radius-lg);
    background: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-glass {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-gradient {
    background: var(--gradient-primary);
    color: white;
}

.card-gradient .card-header {
    background: rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===== MODERN BUTTONS ===== */
.btn {
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;
    text-transform: none;
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary) !important;
    color: white !important;
    box-shadow: var(--shadow-md);
    border: none !important;
}

.btn-primary:hover {
    background: var(--gradient-primary) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white !important;
    border: none !important;
}

.btn-primary:focus,
.btn-primary:active {
    background: var(--gradient-primary) !important;
    color: white !important;
    border: none !important;
    box-shadow: var(--shadow-lg) !important;
}

.btn-success {
    background: var(--gradient-success) !important;
    color: white !important;
    box-shadow: var(--shadow-md);
    border: none !important;
}

.btn-success:hover {
    background: var(--gradient-success) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white !important;
    border: none !important;
}

.btn-success:focus,
.btn-success:active {
    background: var(--gradient-success) !important;
    color: white !important;
    border: none !important;
    box-shadow: var(--shadow-lg) !important;
}

.btn-warning {
    background: var(--gradient-warning) !important;
    color: white !important;
    box-shadow: var(--shadow-md);
    border: none !important;
}

.btn-warning:hover {
    background: var(--gradient-warning) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white !important;
    border: none !important;
}

.btn-warning:focus,
.btn-warning:active {
    background: var(--gradient-warning) !important;
    color: white !important;
    border: none !important;
    box-shadow: var(--shadow-lg) !important;
}

.btn-info {
    background: var(--gradient-info) !important;
    color: white !important;
    box-shadow: var(--shadow-md);
    border: none !important;
}

.btn-info:hover {
    background: var(--gradient-info) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white !important;
    border: none !important;
}

.btn-info:focus,
.btn-info:active {
    background: var(--gradient-info) !important;
    color: white !important;
    border: none !important;
    box-shadow: var(--shadow-lg) !important;
}

.btn-danger {
    background: var(--gradient-danger) !important;
    color: white !important;
    box-shadow: var(--shadow-md);
    border: none !important;
}

.btn-danger:hover {
    background: var(--gradient-danger) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white !important;
    border: none !important;
}

.btn-danger:focus,
.btn-danger:active {
    background: var(--gradient-danger) !important;
    color: white !important;
    border: none !important;
    box-shadow: var(--shadow-lg) !important;
}

.btn-outline-primary {
    border: 2px solid var(--primary-color) !important;
    color: var(--primary-color) !important;
    background: transparent !important;
    font-weight: 600;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary) !important;
    color: white !important;
    transform: translateY(-2px);
    border-color: transparent !important;
    box-shadow: var(--shadow-lg);
}

.btn-outline-success {
    border: 2px solid var(--success-color) !important;
    color: var(--success-color) !important;
    background: transparent !important;
    font-weight: 600;
}

.btn-outline-success:hover {
    background: var(--gradient-success) !important;
    color: white !important;
    transform: translateY(-2px);
    border-color: transparent !important;
    box-shadow: var(--shadow-lg);
}
}

.btn-warning {
    background: var(--warning-color);
    color: white;
}

.btn-light {
    background: white;
    color: var(--dark-color);
    border: 1px solid #e2e8f0;
}

.btn-light:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}

/* ===== MODERN BADGES ===== */
.badge {
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
}

.bg-primary.bg-opacity-10 {
    background-color: rgba(102, 126, 234, 0.1) !important;
}

.bg-success.bg-opacity-10 {
    background-color: rgba(16, 185, 129, 0.1) !important;
}

.bg-warning.bg-opacity-10 {
    background-color: rgba(245, 158, 11, 0.1) !important;
}

.bg-secondary.bg-opacity-10 {
    background-color: rgba(107, 114, 128, 0.1) !important;
}

/* ===== MODERN STATS CARDS ===== */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid #f1f5f9;
    transition: all 0.2s ease;
}

.stats-card:hover {
    box-shadow: var(--shadow);
    transform: translateY(-1px);
}

/* ===== MODERN GRADIENTS ===== */
.bg-gradient-primary-to-secondary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

/* ===== MODERN TABLES ===== */
.table {
    background: white;
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table thead th {
    background: #f8fafc;
    border: none;
    font-weight: 600;
    color: var(--dark-color);
    padding: 1rem;
}

.table tbody td {
    border: none;
    padding: 1rem;
    border-bottom: 1px solid #f1f5f9;
}

.table tbody tr:hover {
    background: #f8fafc;
}

/* ===== MODERN FORMS ===== */
.form-control {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* ===== MODERN AVATARS ===== */
.avatar-sm {
    width: 2.5rem;
    height: 2.5rem;
}

.avatar-md {
    width: 3rem;
    height: 3rem;
}

.avatar-lg {
    width: 4rem;
    height: 4rem;
}

/* ===== MODERN DROPDOWNS ===== */
.dropdown-menu {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 0.5rem;
}

.dropdown-item {
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background: #f1f5f9;
    color: var(--dark-color);
}

.dropdown-header {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== MODERN ALERTS ===== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.25rem;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left: 4px solid var(--success-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left: 4px solid var(--warning-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-left: 4px solid var(--danger-color);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    border-left: 4px solid var(--info-color);
}

/* ===== MODERN NAVIGATION ===== */
.navbar {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-sm);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1rem 0;
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
    text-decoration: none;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: scale(1.05);
}

.nav-link {
    color: var(--gray-700) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 0.5rem 1rem !important;
    border-radius: var(--border-radius-sm);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color) !important;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.nav-link.active {
    color: var(--primary-color) !important;
    background: rgba(102, 126, 234, 0.1);
}

/* ===== MODERN DROPDOWN ===== */
.dropdown-menu {
    border: none;
    box-shadow: var(--shadow-xl);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.dropdown-item {
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
    font-weight: 500;
}

.dropdown-item:hover {
    background: var(--gray-50);
    color: var(--primary-color);
    transform: translateX(4px);
}

.dropdown-header {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* ===== MODERN BADGES ===== */
.badge {
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    letter-spacing: 0.025em;
}

.badge.bg-primary {
    background: var(--gradient-primary) !important;
}

.badge.bg-success {
    background: var(--gradient-success) !important;
}

.badge.bg-warning {
    background: var(--gradient-warning) !important;
}

.badge.bg-info {
    background: var(--gradient-info) !important;
}

.badge.bg-danger {
    background: var(--gradient-danger) !important;
}

/* ===== MODERN SPACING ===== */
.section-padding {
    padding: 4rem 0;
}

.card-padding {
    padding: 2rem;
}

/* ===== MODERN FORMS ===== */
.form-control {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.form-label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
}

.form-select {
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-sm);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* ===== MODERN TABLES ===== */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--shadow);
}

.table thead th {
    background: var(--gradient-primary);
    color: white;
    font-weight: 600;
    border: none;
    padding: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-size: 0.875rem;
}

.table tbody td {
    padding: 1rem;
    border-color: var(--gray-100);
    vertical-align: middle;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: var(--gray-50);
    transform: scale(1.01);
}

/* ===== MODERN ALERTS ===== */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem 1.5rem;
    font-weight: 500;
    border-left: 4px solid;
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-dark);
    border-left-color: var(--success-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-dark);
    border-left-color: var(--warning-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-dark);
    border-left-color: var(--danger-color);
}

.alert-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-dark);
    border-left-color: var(--info-color);
}

/* ===== RESPONSIVE IMPROVEMENTS ===== */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .btn {
        font-size: 0.875rem;
        padding: 0.625rem 1.25rem;
    }

    .stats-card {
        padding: 1.5rem;
    }

    .class-card {
        margin-bottom: 1.5rem;
    }

    .class-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .class-body {
        padding: 1rem 1.5rem 1.5rem;
    }

    .stats-number {
        font-size: 2rem;
    }

    .navbar-brand {
        font-size: 1.25rem;
    }
}

/* ===== MODERN ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.fade-in-up {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-left {
    animation: fadeInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.fade-in-right {
    animation: fadeInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
    animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.pulse {
    animation: pulse 2s infinite;
}

.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* ===== STAGGERED ANIMATIONS ===== */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }
.stagger-6 { animation-delay: 0.6s; }

/* ===== MODERN UTILITY CLASSES ===== */
.bg-gradient-primary {
    background: var(--gradient-primary) !important;
}

.bg-gradient-success {
    background: var(--gradient-success) !important;
}

.bg-gradient-warning {
    background: var(--gradient-warning) !important;
}

.bg-gradient-info {
    background: var(--gradient-info) !important;
}

.bg-gradient-danger {
    background: var(--gradient-danger) !important;
}

.text-primary-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.border-gradient {
    border: 2px solid;
    border-image: var(--gradient-primary) 1;
}

.shadow-soft {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.shadow-strong {
    box-shadow: var(--shadow-2xl);
}

.rounded-modern {
    border-radius: var(--border-radius) !important;
}

.rounded-modern-lg {
    border-radius: var(--border-radius-lg) !important;
}

.rounded-modern-sm {
    border-radius: var(--border-radius-sm) !important;
}

/* ===== TEXT VISIBILITY FIXES ===== */
.text-white {
    color: #ffffff !important;
}

.text-dark {
    color: var(--gray-900) !important;
}

.text-muted {
    color: var(--gray-500) !important;
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

/* ===== BUTTON TEXT FIXES ===== */
.btn .fas,
.btn .fa {
    color: inherit !important;
}

.btn-primary .fas,
.btn-primary .fa,
.btn-success .fas,
.btn-success .fa,
.btn-warning .fas,
.btn-warning .fa,
.btn-info .fas,
.btn-info .fa,
.btn-danger .fas,
.btn-danger .fa {
    color: white !important;
}

/* ===== CARD TEXT FIXES ===== */
.card-header h5,
.card-header h6 {
    color: inherit !important;
    margin-bottom: 0;
}

.card-body h4,
.card-body h5,
.card-body h6 {
    color: var(--gray-900) !important;
}

.card-body p,
.card-body span,
.card-body div {
    color: var(--gray-700) !important;
}

.card-body .small {
    color: var(--gray-500) !important;
}

/* ===== STATS CARD TEXT FIXES ===== */
.stats-card .stats-number.text-primary {
    color: var(--primary-color) !important;
}

.stats-card .stats-number.text-success {
    color: var(--success-color) !important;
}

.stats-card .stats-number.text-warning {
    color: var(--warning-color) !important;
}

.stats-card .stats-number.text-info {
    color: var(--info-color) !important;
}

.stats-card .stats-number.text-danger {
    color: var(--danger-color) !important;
}

/* ===== GRADIENT BACKGROUND TEXT FIXES ===== */
.bg-gradient-primary,
.bg-gradient-success,
.bg-gradient-warning,
.bg-gradient-info,
.bg-gradient-danger {
    color: white !important;
}

.bg-gradient-primary *,
.bg-gradient-success *,
.bg-gradient-warning *,
.bg-gradient-info *,
.bg-gradient-danger * {
    color: white !important;
}

/* ===== MODERN STATISTICS CARDS ===== */
.stats-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stats-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stats-card.stats-primary::before {
    background: var(--gradient-primary);
}

.stats-card.stats-success::before {
    background: var(--gradient-success);
}

.stats-card.stats-warning::before {
    background: var(--gradient-warning);
}

.stats-card.stats-info::before {
    background: var(--gradient-info);
}

.stats-card.stats-danger::before {
    background: var(--gradient-danger);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 0.5rem;
    color: inherit !important;
}

.stats-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-600) !important;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.stats-icon.icon-primary {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.stats-icon.icon-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.stats-icon.icon-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.stats-icon.icon-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.stats-icon.icon-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

/* ===== MODERN CLASS CARDS ===== */
.class-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.class-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.class-card:hover {
    transform: translateY(-12px);
    box-shadow: var(--shadow-2xl);
}

.class-card:hover::before {
    opacity: 1;
}

.class-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--gray-100);
}

.class-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900) !important;
    margin-bottom: 0.5rem;
}

.class-subtitle {
    font-size: 0.875rem;
    color: var(--gray-500) !important;
    font-weight: 500;
}

.class-body {
    padding: 1.5rem 2rem 2rem;
}

.wali-info {
    background: var(--gray-50);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid var(--primary-color);
}

.wali-label {
    font-size: 0.75rem;
    color: var(--gray-500) !important;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.wali-name {
    font-weight: 600;
    color: var(--gray-900) !important;
}

/* ===== MODERN UTILITIES ===== */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, var(--primary-color), var(--secondary-color)) border-box;
}
