# SISTEM CURA PERSONALIS
# Seminari Menengah Santo <PERSON>

## DESKRIPSI
Sistem manajemen data siswa seminari dengan fokus pada pendampingan dan pengembangan karakter siswa. Sistem ini memungkinkan wali kelas untuk mengelola data siswa di kelas masing-masing dengan sistem login yang terpisah.

## FITUR UTAMA
1. **Sistem Login Multi-User**
   - Admin: Aks<PERSON> penuh ke semua fitur
   - Wali Kelas: Akses terbatas hanya ke kelas yang di-assign

2. **Manajemen Data Siswa**
   - Data pribadi siswa lengkap
   - Foto siswa
   - Catatan pendampingan
   - Status anak potensial dan catatan khusus

3. **Dashboard Admin**
   - Kelola users (wali kelas)
   - Assign kelas ke wali kelas
   - Kelola tahun pelajaran
   - Statistik sistem

4. **Fitur Wali Kelas**
   - G<PERSON>baran umum kelas
   - Data siswa per kelas
   - Detail siswa individual
   - Daftar anak potensial
   - Daftar anak catatan khusus

## INSTALASI

### 1. Persiapan Server
- XAMPP (Apache + MySQL + PHP)
- PHP 7.4 atau lebih baru
- MySQL 5.7 atau lebih baru

### 2. Setup Database
1. Buka phpMyAdmin
2. Buat database baru dengan nama: `cura_personalis`
3. Import file `install.sql` ke database tersebut

### 3. Konfigurasi
1. Edit file `includes/db.php` sesuai konfigurasi database Anda
2. Pastikan folder `uploads/foto/` dapat ditulis (writable)

### 4. Akses Sistem
- URL: http://localhost/cura_personalis
- Akan otomatis redirect ke halaman login

## LOGIN DEFAULT

### Administrator
- Username: `admin`
- Password: `password`

### Wali Kelas (Contoh)
- Username: `wali_kppa`
- Password: `password`
- Akses: KPP A

- Username: `wali_kppb`
- Password: `password`
- Akses: KPP B

- Username: `wali_x1`
- Password: `password`
- Akses: X-1

## STRUKTUR SISTEM

### Database Tables
- `users`: Data pengguna sistem
- `user_kelas`: Relasi user dengan kelas yang dapat diakses
- `kelas`: Data kelas
- `tahun_pelajaran`: Data tahun pelajaran
- `siswa`: Data siswa
- `kelas_gambaran_umum`: Statistik dan gambaran umum kelas

### File Structure
```
cura_personalis/
├── admin/              # Halaman administrator
├── guru/               # Halaman wali kelas
├── includes/           # File konfigurasi dan template
├── assets/             # CSS, JS, gambar
├── uploads/            # File upload (foto siswa)
├── install.sql         # Schema database
├── login.php           # Halaman login
├── logout.php          # Logout
└── index.php           # Redirect ke login
```

## PANDUAN PENGGUNAAN

### Untuk Administrator
1. Login dengan akun admin
2. Akses Dashboard Admin untuk:
   - Mengelola users wali kelas
   - Mengatur assignment kelas
   - Mengelola tahun pelajaran
   - Melihat statistik sistem

### Untuk Wali Kelas
1. Login dengan akun wali kelas
2. Pilih kelas dari dropdown menu
3. Akses fitur:
   - Gambaran Umum: Statistik kelas
   - Data Siswa: Daftar siswa di kelas
   - Detail Siswa: Informasi lengkap siswa
   - Anak Potensial: Siswa dengan potensi panggilan
   - Catatan Khusus: Siswa yang perlu perhatian khusus

## KEAMANAN
- Password di-hash menggunakan PHP password_hash()
- Prepared statements untuk mencegah SQL injection
- Session management untuk autentikasi
- Access control berdasarkan role dan assignment kelas

## PENGEMBANGAN SELANJUTNYA
- CRUD operations untuk data siswa
- Upload dan manajemen foto siswa
- Laporan dan export data
- Sistem backup otomatis
- Notifikasi dan reminder

## SUPPORT
Untuk bantuan teknis atau pertanyaan, hubungi administrator sistem.

---
Dikembangkan untuk Seminari Menengah Santo Paulus Mataloko
© 2024 Cura Personalis System