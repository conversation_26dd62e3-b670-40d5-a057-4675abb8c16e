-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- W<PERSON><PERSON> pembuatan: 05 Agu 2025 pada 15.29
-- Versi server: 10.4.27-MariaDB
-- Versi PHP: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `cura_personalis`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON> dari tabel `kelas`
--

CREATE TABLE `kelas` (
  `id_kelas` int(11) NOT NULL,
  `nama_kelas` varchar(50) NOT NULL,
  `tingkat` varchar(20) DEFAULT NULL,
  `hasil_komitmen_bersama` text DEFAULT NULL,
  `aspek_sanitas` text DEFAULT NULL,
  `aspek_sactitas` text DEFAULT NULL,
  `aspek_scientia` text DEFAULT NULL,
  `mapel_agama` text DEFAULT NULL,
  `mapel_bahasa_indonesia` text DEFAULT NULL,
  `mapel_bahasa_inggris` text DEFAULT NULL,
  `mapel_bahasa_latin` text DEFAULT NULL,
  `mapel_lain` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `kelas`
--

INSERT INTO `kelas` (`id_kelas`, `nama_kelas`, `tingkat`, `hasil_komitmen_bersama`, `aspek_sanitas`, `aspek_sactitas`, `aspek_scientia`, `mapel_agama`, `mapel_bahasa_indonesia`, `mapel_bahasa_inggris`, `mapel_bahasa_latin`, `mapel_lain`, `created_at`) VALUES
(1, 'KPP A', 'Kelas Persiapan Pert', '', '', '', '', '', '', '', '', '', '2025-08-04 12:05:27'),
(2, 'KPP B', 'Kelas Persiapan Pert', '', '', '', '', '', '', '', '', '', '2025-08-04 12:05:27'),
(3, 'X-1', 'Kelas 10', '', '', '', '', '', '', '', '', '', '2025-08-04 12:05:27'),
(4, 'X-2', 'Kelas 10', '', '', '', '', '', '', '', '', '', '2025-08-04 12:05:27'),
(5, 'XI-1', 'Kelas 11', '', '', '', '', '', '', '', '', '', '2025-08-04 12:05:27'),
(6, 'XI-2', 'Kelas 11', '', '', '', '', '', '', '', '', '', '2025-08-04 12:05:27'),
(7, 'XII-1', 'Kelas 12', '', '', '', '', '', '', '', '', '', '2025-08-04 12:05:27'),
(8, 'XII-2', 'Kelas 12', '', '', '', '', '', '', '', '', '', '2025-08-04 12:05:27'),
(9, 'KPP C', 'Kelas Persiapan Pert', '', '', '', '', '', '', '', '', '', '2025-08-04 12:15:40'),
(10, 'KPA', 'Kelas Persiapan Akhi', '', '', '', '', '', '', '', '', '', '2025-08-04 12:16:13');

-- --------------------------------------------------------

--
-- Struktur dari tabel `kelas_gambaran_umum`
--

CREATE TABLE `kelas_gambaran_umum` (
  `id` int(11) NOT NULL,
  `id_kelas` int(11) NOT NULL,
  `id_tahun` int(11) NOT NULL,
  `komitmen_bersama` text DEFAULT NULL,
  `rata_rata_nilai` decimal(5,2) DEFAULT NULL,
  `nilai_tertinggi` decimal(5,2) DEFAULT NULL,
  `nilai_terendah` decimal(5,2) DEFAULT NULL,
  `rangking_5_terbawah` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`rangking_5_terbawah`)),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Struktur dari tabel `siswa`
--

CREATE TABLE `siswa` (
  `id_siswa` int(11) NOT NULL,
  `nama_lengkap` varchar(100) NOT NULL,
  `nama_panggilan` varchar(50) DEFAULT NULL,
  `asal_paroki` varchar(100) DEFAULT NULL,
  `keuskupan` varchar(100) DEFAULT NULL,
  `status_ppdb` varchar(50) DEFAULT NULL,
  `gambaran_umum` text DEFAULT NULL,
  `kondisi_saat_ini` text DEFAULT NULL,
  `catatan` text DEFAULT NULL,
  `catatan_lain` text DEFAULT NULL,
  `catatan_kepamongan` text DEFAULT NULL,
  `pendampingan` text DEFAULT NULL,
  `hasil_pendampingan` text DEFAULT NULL,
  `nilai_rata_rata` decimal(5,2) DEFAULT NULL,
  `foto` varchar(255) DEFAULT 'placeholder.jpg',
  `id_kelas` int(11) NOT NULL,
  `id_tahun` int(11) NOT NULL,
  `is_potensial` tinyint(1) DEFAULT 0,
  `is_catatan_khusus` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `siswa`
--

INSERT INTO `siswa` (`id_siswa`, `nama_lengkap`, `nama_panggilan`, `asal_paroki`, `keuskupan`, `status_ppdb`, `gambaran_umum`, `kondisi_saat_ini`, `catatan`, `catatan_lain`, `catatan_kepamongan`, `pendampingan`, `hasil_pendampingan`, `nilai_rata_rata`, `foto`, `id_kelas`, `id_tahun`, `is_potensial`, `is_catatan_khusus`, `created_at`, `updated_at`) VALUES
(1, 'Yohanes Paulus Simbolon', 'Paulus', 'Paroki Kristus Raja Jakarta', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', '77.80', 'placeholder.jpg', 1, 2, 0, 1, '2025-08-04 12:05:27', '2025-08-04 23:58:10'),
(2, 'Petrus Kanisius Hutabarat', 'Petrus', 'Santo Yosef Medari', 'KAS', 'Pantas', '', '', '', NULL, NULL, '', '', '86.70', 'siswa_2_1754313033.jpg', 1, 2, 1, 0, '2025-08-04 12:05:27', '2025-08-04 23:58:10'),
(3, 'Fransiskus Xaverius Manalu', 'Frans', 'Paroki Santa Maria', 'Keuskupan Padang', 'Reguler', NULL, NULL, NULL, NULL, NULL, NULL, NULL, '62.50', 'placeholder.jpg', 2, 2, 0, 0, '2025-08-04 12:05:27', '2025-08-04 23:58:10'),
(4, 'Abel Georgeryan Paskalis', 'Abel', 'St. Servatius Kampung Sawah', 'Non KAS', 'Diterima', '', 'uytu', '', 'jkjkjk', 'jkjkjkl', '', '', '85.00', 'placeholder.jpg', 1, 1, 0, 0, '2025-08-04 23:59:15', '2025-08-05 12:55:27'),
(5, 'Bernardin Ramaleon Herbektratama', '', 'St. Yohanes Rasul Wonogiri', 'KAS', 'Diterima', '', '', '', '', '- Melakukan pelangaran tidak membaca buku refleksi dikenakan SP 1 pada tanggal 01 Agustus 2025\r\n- Tidak mengikuti setudi 2 malah nongkrong diangkringan dikenakan SP 2 pada tanggal 04 Agustus 2025\r\n- Tidak mengikuti opra siang malah makan jambu dikenakan SP 3 pada tanggal 06 Agustus 2025', '', '', '70.00', 'siswa_5_1754355687.png', 1, 1, 0, 0, '2025-08-05 00:52:57', '2025-08-05 13:05:54'),
(6, 'Claudius Valentino Wijaya', '', 'Tyas Dalem Gusti Yesus Macanan', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_6_1754365634.png', 1, 1, 0, 0, '2025-08-05 00:53:38', '2025-08-05 03:47:15'),
(7, 'Dhwen Requel Efrem Surbakti', '', 'St. Fransiskus Assisi Padang Bulan Medan', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_7_1754365664.png', 1, 1, 0, 0, '2025-08-05 00:53:58', '2025-08-05 03:47:44'),
(8, 'Dyonisius Prabu Mangun Raharja', '', 'St. Petrus Purwosari', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_8_1754365677.png', 1, 1, 0, 0, '2025-08-05 00:54:28', '2025-08-05 03:47:57'),
(9, 'Fernando Jovian Subianto', '', 'St. Joan Don Bosco Sampit', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_9_1754365689.png', 1, 1, 0, 0, '2025-08-05 00:54:44', '2025-08-05 03:48:09'),
(10, 'Gabriel Aldrino Narawangsa', '', 'St. Martinus Bandung', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_10_1754365700.png', 1, 1, 0, 0, '2025-08-05 00:55:00', '2025-08-05 03:48:20'),
(11, 'Gerardus Jalu Christo', '', 'Maria Tak Bernoda Kepanjen', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_11_1754365709.png', 1, 1, 0, 0, '2025-08-05 00:55:23', '2025-08-05 03:48:30'),
(12, 'Guardian Gabbriel Enavannez', '', 'St. Fransiskus Asisi Padang', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_12_1754365725.png', 1, 1, 0, 0, '2025-08-05 00:55:39', '2025-08-05 03:48:45'),
(13, 'Ignatius Loyola Puji Panuwun Agung', '', 'St. Petrus Sambiroto', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'placeholder.jpg', 1, 1, 0, 0, '2025-08-05 00:55:55', '2025-08-05 00:55:55'),
(14, 'Joel Christian Hadinata', '', 'St. Yakobus Kelapa Gading', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_14_1754365742.png', 1, 1, 0, 0, '2025-08-05 00:56:09', '2025-08-05 03:49:02'),
(15, 'Joseph Johanes Palimbong', '', 'St. Petrus Batam', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_15_1754365754.png', 1, 1, 0, 0, '2025-08-05 00:56:23', '2025-08-05 03:49:15'),
(16, 'Justine Putra Urip Widodo', '', 'Santa Perawan Maria Regina Purbowardayan', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_16_1754365773.png', 1, 1, 0, 0, '2025-08-05 00:56:36', '2025-08-05 03:49:33'),
(17, 'Klemens Martin Pratama', '', 'Kristus Raja Cigugur', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_17_1754365787.png', 1, 1, 0, 0, '2025-08-05 00:56:49', '2025-08-05 03:49:47'),
(18, 'Leonard Purnomo Aji Sihotang', '', 'St. Fransiskus Asisi Padang', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_18_1754365796.png', 1, 1, 0, 0, '2025-08-05 00:57:05', '2025-08-05 03:49:57'),
(19, 'Nickolas Cesario Aristoputra', '', 'St. Servatius Kampung Sawah', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_19_1754365811.png', 1, 1, 0, 0, '2025-08-05 00:57:18', '2025-08-05 03:50:11'),
(20, 'Raymondus Marvel Bima Prasetya', '', 'Santa Theresia Lisieux Boro', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_20_1754365824.png', 1, 1, 0, 0, '2025-08-05 00:57:34', '2025-08-05 03:50:26'),
(21, 'Rizky Adi Prasetyo', '', 'St. Yusuf Gedangan', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_21_1754365839.png', 1, 1, 0, 0, '2025-08-05 00:57:47', '2025-08-05 03:50:39'),
(22, 'Samuel Albari Setiawan', '', 'St. Markus Depok', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_22_1754365850.png', 1, 1, 0, 0, '2025-08-05 00:58:00', '2025-08-05 03:50:50'),
(23, 'Satria Purnama Seta', '', 'St. Yakobus Bantul', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_23_1754365861.png', 1, 1, 0, 0, '2025-08-05 00:58:15', '2025-08-05 03:51:02'),
(24, 'Abraham Marcello Saga Pramudoyo', '', 'St. Athanasius Agung Karangpanas', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_24_1754359999.png', 9, 1, 0, 0, '2025-08-05 01:18:22', '2025-08-05 02:13:19'),
(25, 'Ambrosius Bondan Sudarmono', '', 'Hati Kudus Tuhan Yesus Ganjuran', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_25_1754357231.png', 9, 1, 0, 0, '2025-08-05 01:18:38', '2025-08-05 01:27:11'),
(26, 'Andreas Donatus Elta Prihandono', '', 'Hati Tersuci Santa Perawan Maria Sempusari', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_26_1754359672.png', 9, 1, 0, 0, '2025-08-05 01:19:09', '2025-08-05 02:07:52'),
(27, 'Benediktus Jonas Juliano Perdana', '', 'St. Pius X Karanganyar', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_27_1754359976.png', 9, 1, 0, 0, '2025-08-05 01:19:30', '2025-08-05 02:12:56'),
(28, 'Bernadus Damar Adi Kriswanto', '', 'Paroki Gregorius Agung Kutabumi Tangerang', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_28_1754360045.png', 9, 1, 0, 0, '2025-08-05 01:19:49', '2025-08-05 02:14:05'),
(29, 'Bernadus Rafael Nicolas Bintang Dewanto', '', 'Santa Maria Diangkat Ke Surga Palur', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_29_1754365554.png', 9, 1, 0, 0, '2025-08-05 01:20:39', '2025-08-05 03:45:54'),
(30, 'Ferdinand Satwika HD', '', 'St. Fransiskus Asisi Padang Bulan', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'placeholder.jpg', 9, 1, 0, 0, '2025-08-05 01:20:57', '2025-08-05 01:20:57'),
(31, 'Gabriel Alvaro Wahyu Murtanto', '', 'Kristus Raja Ungaran', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_31_1754360217.png', 9, 1, 0, 0, '2025-08-05 01:21:10', '2025-08-05 02:16:58'),
(32, 'Gabriel Banyu Zelig Sanjono', '', 'St. Yusup Pekerja Mertoyudan', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_32_1754359874.png', 9, 1, 0, 0, '2025-08-05 01:21:24', '2025-08-05 02:11:14'),
(33, 'Gabriel Danendra Abhinaya', '', 'Santa Bernadet Pinang', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'placeholder.jpg', 9, 1, 0, 0, '2025-08-05 01:21:42', '2025-08-05 01:21:42'),
(34, 'Gabriel Narendra Solideo', '', 'St. Alfonsus Maria De Liguori Nandan', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_34_1754359589.png', 9, 1, 0, 0, '2025-08-05 01:21:56', '2025-08-05 02:06:29'),
(35, 'Gregorius Krisna Samiaji Wardoyo', '', 'St. Antonius Muntilan', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_35_1754359789.png', 9, 1, 0, 0, '2025-08-05 01:22:10', '2025-08-05 02:09:49'),
(36, 'Heribertus Obed Purlitojati', '', 'St. Aloysius Gonzaga Mlati', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_36_1754359738.png', 9, 1, 0, 0, '2025-08-05 01:22:23', '2025-08-05 02:08:58'),
(37, 'Hilarius Arane Mahatma Abimantrana', '', 'St. Petrus Kanisius Wonosari', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_37_1754360083.png', 9, 1, 0, 0, '2025-08-05 01:22:37', '2025-08-05 02:14:43'),
(38, 'Isidorus Fausta Yoga Hestungkara', '', 'Tyas Dalem Gusti Yesus Macanan', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_38_1754365591.png', 9, 1, 0, 0, '2025-08-05 01:22:50', '2025-08-05 03:46:31'),
(39, 'Laurensius Vinant Kalani', '', 'Kristus Raja Ungaran', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_39_1754359704.png', 9, 1, 0, 0, '2025-08-05 01:23:01', '2025-08-05 02:08:24'),
(40, 'Mathew Vido Carlo', '', 'St. Athanasius Agung Karangpanas', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_40_1754359814.png', 9, 1, 0, 0, '2025-08-05 01:23:16', '2025-08-05 02:10:14'),
(41, 'Matteo Kenzie Rana', '', 'Hati Kudus Yesus Jonggol', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_41_1754357308.png', 9, 1, 0, 0, '2025-08-05 01:23:36', '2025-08-05 01:28:28'),
(42, 'Mikhael Prasetyo Lokeswara Putra A', '', 'Paroki St. Maria Immaculata Slawi', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_42_1754359638.png', 9, 1, 0, 0, '2025-08-05 01:24:12', '2025-08-05 02:07:18'),
(43, 'Nathanael Gaspar Pranatsukma', '', 'Santa Maria Para Malaikat Cipanas', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_43_1754359499.png', 9, 1, 0, 0, '2025-08-05 01:24:26', '2025-08-05 02:04:59'),
(44, 'Nereus Raditya Nayrendra Ji Lenjau', '', 'Santa Maria Assumpta Tanjung Selor', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_44_1754359896.png', 9, 1, 0, 0, '2025-08-05 01:24:39', '2025-08-05 02:11:36'),
(45, 'Petrus Donisetiawan Maturbongs', '', 'St. Gregorius Agung Tangerang', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_45_1754360161.png', 9, 1, 0, 0, '2025-08-05 01:24:54', '2025-08-05 02:16:01'),
(46, 'Sebastianus Pradipta Saktyawan', '', 'St. Paulus Kleca Surakarta', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_46_1754360271.png', 9, 1, 0, 0, '2025-08-05 01:25:06', '2025-08-05 02:17:51'),
(47, 'Thomas Herjuno Pangestu', '', 'Santa Odilia Citra Raya Tangerang', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_47_1754359843.png', 9, 1, 0, 0, '2025-08-05 01:25:19', '2025-08-05 02:10:43'),
(48, 'Yohanes Muriellus Lionel Hariyadi', '', 'Santa Maria Annuntiata Sidoarjo', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_48_1754360139.png', 9, 1, 0, 0, '2025-08-05 01:25:32', '2025-08-05 02:15:39'),
(49, 'Adhitya Setya Pratama', '', 'St. Thomas Rasul Bedono', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'placeholder.jpg', 2, 1, 0, 0, '2025-08-05 03:54:02', '2025-08-05 03:54:02'),
(50, 'Alfonsus Maria De Liguori Wahyu Atmaja', '', 'St. Antonius Padua Muntilan', 'KAS', 'Pantas', '', '', '', NULL, NULL, '', '', NULL, 'siswa_new_1754366066.png', 2, 1, 0, 0, '2025-08-05 03:54:26', '2025-08-05 03:54:26'),
(51, 'Bagas Janu Irawan', '', 'St. Kristoforus Banyutemumpang', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_new_1754366087.png', 2, 1, 0, 0, '2025-08-05 03:54:47', '2025-08-05 03:54:47'),
(52, 'Baptista Varani Arley Dipa Darmawan', '', 'St. Yosep Purwokerto', 'Non KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'siswa_new_1754366114.png', 2, 1, 0, 0, '2025-08-05 03:55:14', '2025-08-05 03:55:14'),
(53, 'Chrisantus Lanang Waskita Radityo', '', 'Tyas Dalem Gusti Yesus Macanan', 'KAS', 'Pantas', '', '', '', NULL, NULL, '', '', NULL, 'siswa_new_1754366148.png', 2, 1, 0, 0, '2025-08-05 03:55:48', '2025-08-05 03:55:48'),
(54, 'Cornelius Nathanael Hadi', '', 'St. Kristoforus Grogol', 'Non KAS', 'Pantas', '', '', '', NULL, NULL, '', '', NULL, 'siswa_new_1754366179.png', 2, 1, 0, 0, '2025-08-05 03:56:20', '2025-08-05 03:56:20'),
(55, 'Dionisius Bintang Permana', '', 'St. Theresia Lisieux Boro', 'KAS', 'Diterima', '', '', '', NULL, NULL, '', '', NULL, 'placeholder.jpg', 2, 1, 0, 0, '2025-08-05 03:56:39', '2025-08-05 03:56:39');

-- --------------------------------------------------------

--
-- Struktur dari tabel `tahun_pelajaran`
--

CREATE TABLE `tahun_pelajaran` (
  `id_tahun` int(11) NOT NULL,
  `tahun_ajaran` varchar(20) NOT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `tahun_pelajaran`
--

INSERT INTO `tahun_pelajaran` (`id_tahun`, `tahun_ajaran`, `is_active`, `created_at`) VALUES
(1, '2025/2026', 1, '2025-08-04 12:05:27'),
(2, '2024/2025', 0, '2025-08-04 12:05:27');

-- --------------------------------------------------------

--
-- Struktur dari tabel `users`
--

CREATE TABLE `users` (
  `id_user` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `nama_lengkap` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `role` enum('admin','wali_kelas') DEFAULT 'wali_kelas',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `users`
--

INSERT INTO `users` (`id_user`, `username`, `password`, `nama_lengkap`, `email`, `role`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', '<EMAIL>', 'admin', 1, '2025-08-04 12:05:27', '2025-08-04 12:05:27'),
(2, 'wali_kppa', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Maria Agustina Reforma Putri, S.Pd', '<EMAIL>', 'wali_kelas', 1, '2025-08-04 12:05:27', '2025-08-05 01:16:11'),
(3, 'wali_kppb', '$2y$10$VxNxe5rCoMXN.DDvJiWrPeuOyjzQl8rCEUoOaIPJvvNG4RzqOHpY.', 'Elisabeth  Averryane Suminto, M.Pd.', '<EMAIL>', 'wali_kelas', 1, '2025-08-04 12:05:27', '2025-08-05 01:16:34'),
(4, 'wali_x1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Wali kelas X-1', '<EMAIL>', 'wali_kelas', 1, '2025-08-04 12:05:27', '2025-08-04 14:54:35'),
(5, 'wali_kppc', '$2y$10$sewAC1UHiz4N71fl8A8eFu6bCDtq/3wHRnyZT2WyR4znEV2C9pXPy', 'Agnes Wardani, S.Pd.', '<EMAIL>', 'wali_kelas', 1, '2025-08-05 01:15:49', '2025-08-05 01:15:49');

-- --------------------------------------------------------

--
-- Struktur dari tabel `user_kelas`
--

CREATE TABLE `user_kelas` (
  `id` int(11) NOT NULL,
  `id_user` int(11) NOT NULL,
  `id_kelas` int(11) NOT NULL,
  `id_tahun` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data untuk tabel `user_kelas`
--

INSERT INTO `user_kelas` (`id`, `id_user`, `id_kelas`, `id_tahun`, `created_at`) VALUES
(1, 2, 1, 2, '2025-08-04 12:05:27'),
(2, 3, 2, 2, '2025-08-04 12:05:27'),
(3, 4, 3, 2, '2025-08-04 12:05:27'),
(4, 2, 1, 1, '2025-08-05 00:46:29'),
(5, 3, 2, 1, '2025-08-05 01:13:37'),
(6, 5, 9, 1, '2025-08-05 01:16:48');

--
-- Indexes for dumped tables
--

--
-- Indeks untuk tabel `kelas`
--
ALTER TABLE `kelas`
  ADD PRIMARY KEY (`id_kelas`);

--
-- Indeks untuk tabel `kelas_gambaran_umum`
--
ALTER TABLE `kelas_gambaran_umum`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_kelas_tahun` (`id_kelas`,`id_tahun`),
  ADD KEY `id_tahun` (`id_tahun`);

--
-- Indeks untuk tabel `siswa`
--
ALTER TABLE `siswa`
  ADD PRIMARY KEY (`id_siswa`),
  ADD KEY `id_kelas` (`id_kelas`),
  ADD KEY `id_tahun` (`id_tahun`);

--
-- Indeks untuk tabel `tahun_pelajaran`
--
ALTER TABLE `tahun_pelajaran`
  ADD PRIMARY KEY (`id_tahun`);

--
-- Indeks untuk tabel `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id_user`),
  ADD UNIQUE KEY `username` (`username`);

--
-- Indeks untuk tabel `user_kelas`
--
ALTER TABLE `user_kelas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_kelas_tahun` (`id_user`,`id_kelas`,`id_tahun`),
  ADD KEY `id_kelas` (`id_kelas`),
  ADD KEY `id_tahun` (`id_tahun`);

--
-- AUTO_INCREMENT untuk tabel yang dibuang
--

--
-- AUTO_INCREMENT untuk tabel `kelas`
--
ALTER TABLE `kelas`
  MODIFY `id_kelas` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT untuk tabel `kelas_gambaran_umum`
--
ALTER TABLE `kelas_gambaran_umum`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT untuk tabel `siswa`
--
ALTER TABLE `siswa`
  MODIFY `id_siswa` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=56;

--
-- AUTO_INCREMENT untuk tabel `tahun_pelajaran`
--
ALTER TABLE `tahun_pelajaran`
  MODIFY `id_tahun` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT untuk tabel `users`
--
ALTER TABLE `users`
  MODIFY `id_user` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT untuk tabel `user_kelas`
--
ALTER TABLE `user_kelas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- Ketidakleluasaan untuk tabel pelimpahan (Dumped Tables)
--

--
-- Ketidakleluasaan untuk tabel `kelas_gambaran_umum`
--
ALTER TABLE `kelas_gambaran_umum`
  ADD CONSTRAINT `kelas_gambaran_umum_ibfk_1` FOREIGN KEY (`id_kelas`) REFERENCES `kelas` (`id_kelas`),
  ADD CONSTRAINT `kelas_gambaran_umum_ibfk_2` FOREIGN KEY (`id_tahun`) REFERENCES `tahun_pelajaran` (`id_tahun`);

--
-- Ketidakleluasaan untuk tabel `siswa`
--
ALTER TABLE `siswa`
  ADD CONSTRAINT `siswa_ibfk_1` FOREIGN KEY (`id_kelas`) REFERENCES `kelas` (`id_kelas`),
  ADD CONSTRAINT `siswa_ibfk_2` FOREIGN KEY (`id_tahun`) REFERENCES `tahun_pelajaran` (`id_tahun`);

--
-- Ketidakleluasaan untuk tabel `user_kelas`
--
ALTER TABLE `user_kelas`
  ADD CONSTRAINT `user_kelas_ibfk_1` FOREIGN KEY (`id_user`) REFERENCES `users` (`id_user`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_kelas_ibfk_2` FOREIGN KEY (`id_kelas`) REFERENCES `kelas` (`id_kelas`) ON DELETE CASCADE,
  ADD CONSTRAINT `user_kelas_ibfk_3` FOREIGN KEY (`id_tahun`) REFERENCES `tahun_pelajaran` (`id_tahun`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
