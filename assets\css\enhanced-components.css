/* Enhanced Components for Cura Personalis */

/* ===== ENHANCED LOADING STATES ===== */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.loading-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    box-shadow: var(--shadow);
}

.loading-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.loading-text.short {
    width: 60%;
}

.loading-text.medium {
    width: 80%;
}

.loading-text.long {
    width: 100%;
}

/* ===== ENHANCED TOOLTIPS ===== */
.tooltip-modern {
    position: relative;
    cursor: help;
}

.tooltip-modern::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip-modern::after {
    content: '';
    position: absolute;
    bottom: 115%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--gray-900);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tooltip-modern:hover::before,
.tooltip-modern:hover::after {
    opacity: 1;
    visibility: visible;
}

/* ===== ENHANCED PROGRESS BARS ===== */
.progress-modern {
    height: 8px;
    border-radius: 50px;
    background: var(--gray-200);
    overflow: hidden;
    position: relative;
}

.progress-bar-modern {
    height: 100%;
    border-radius: 50px;
    background: var(--gradient-primary);
    transition: width 0.6s ease;
    position: relative;
    overflow: hidden;
}

.progress-bar-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* ===== ENHANCED MODALS ===== */
.modal-modern .modal-dialog {
    max-width: 600px;
    margin: 2rem auto;
}

.modal-modern .modal-content {
    border: none;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
}

.modal-modern .modal-header {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 2rem;
}

.modal-modern .modal-title {
    font-weight: 700;
    font-size: 1.25rem;
}

.modal-modern .modal-body {
    padding: 2rem;
}

.modal-modern .modal-footer {
    border: none;
    padding: 1rem 2rem 2rem;
    background: var(--gray-50);
}

/* ===== ENHANCED TABS ===== */
.nav-tabs-modern {
    border: none;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    padding: 0.25rem;
}

.nav-tabs-modern .nav-link {
    border: none;
    border-radius: var(--border-radius-sm);
    color: var(--gray-600);
    font-weight: 500;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.nav-tabs-modern .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
}

.nav-tabs-modern .nav-link.active {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

/* ===== ENHANCED PAGINATION ===== */
.pagination-modern {
    gap: 0.5rem;
}

.pagination-modern .page-link {
    border: none;
    border-radius: var(--border-radius-sm);
    color: var(--gray-600);
    font-weight: 500;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.pagination-modern .page-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.pagination-modern .page-item.active .page-link {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

/* ===== ENHANCED BREADCRUMBS ===== */
.breadcrumb-modern {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-modern .breadcrumb-item {
    font-weight: 500;
}

.breadcrumb-modern .breadcrumb-item + .breadcrumb-item::before {
    content: '›';
    color: var(--gray-400);
    font-weight: 600;
    font-size: 1.2rem;
}

.breadcrumb-modern .breadcrumb-item.active {
    color: var(--primary-color);
}

/* ===== ENHANCED SEARCH ===== */
.search-modern {
    position: relative;
    max-width: 400px;
}

.search-modern .form-control {
    padding-left: 3rem;
    border-radius: 50px;
    border: 2px solid var(--gray-200);
    transition: all 0.3s ease;
}

.search-modern .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    padding-left: 3.5rem;
}

.search-modern .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    transition: all 0.3s ease;
    z-index: 5;
}

.search-modern .form-control:focus + .search-icon {
    color: var(--primary-color);
    left: 1.25rem;
}

/* ===== ENHANCED NOTIFICATIONS ===== */
.notification-modern {
    position: fixed;
    top: 2rem;
    right: 2rem;
    max-width: 400px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-xl);
    padding: 1.5rem;
    border-left: 4px solid var(--primary-color);
    z-index: 1050;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification-modern.show {
    transform: translateX(0);
}

.notification-modern.success {
    border-left-color: var(--success-color);
}

.notification-modern.warning {
    border-left-color: var(--warning-color);
}

.notification-modern.error {
    border-left-color: var(--danger-color);
}

/* ===== RESPONSIVE ENHANCEMENTS ===== */
@media (max-width: 768px) {
    .modal-modern .modal-dialog {
        margin: 1rem;
    }
    
    .modal-modern .modal-header,
    .modal-modern .modal-body {
        padding: 1.5rem;
    }
    
    .notification-modern {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
    
    .search-modern {
        max-width: 100%;
    }
}
