# Quick Fix untuk Error Kondisi Akademik

## Error yang <PERSON>
```
Fatal error: Uncaught Error: Call to a member function bind_param() on bool in gambaran_kelas.php:154
```

## Penyebab
Error ini terjadi karena kolom `nilai_rata_rata` belum ditambahkan ke database, sehingga query SQL gagal dipersiapkan.

## Solusi Cepat

### Opsi 1: Jalankan Script SQL (Recommended)
1. Buka **phpMyAdmin**
2. Pilih database `cura_personalis`
3. Klik tab **SQL**
4. Copy-paste dan jalankan script berikut:

```sql
-- <PERSON><PERSON> kolom nilai_rata_rata ke tabel siswa
ALTER TABLE siswa ADD COLUMN nilai_rata_rata DECIMAL(5,2) DEFAULT NULL AFTER hasil_pendampingan;

-- Verifikasi kolom sudah ditambahkan
SHOW COLUMNS FROM siswa LIKE 'nilai_rata_rata';
```

### Opsi 2: Jalankan File SQL yang Sudah Disediakan
1. Buka **phpMyAdmin**
2. Pilih database `cura_personalis`
3. Klik tab **Import**
4. Upload file `add_academic_features.sql`
5. Klik **Go**

### Opsi 3: Jalankan Script PHP
1. Buka browser
2. Akses: `http://localhost/cura_personalis/add_sample_grades.php`
3. Script akan otomatis menambah kolom jika belum ada

## Verifikasi Fix Berhasil

### 1. Cek Kolom Database
Jalankan query ini di phpMyAdmin:
```sql
DESCRIBE siswa;
```
Pastikan ada kolom `nilai_rata_rata` dengan tipe `decimal(5,2)`.

### 2. Test Halaman Gambaran Kelas
1. Login ke aplikasi
2. Buka menu **Gambaran Kelas**
3. Seharusnya tidak ada error lagi
4. Akan muncul section **Kondisi Akademik**

### 3. Test Input Nilai
1. Buka menu **Edit Siswa**
2. Scroll ke bawah
3. Seharusnya ada field **Nilai Rata-rata**

## Fitur Setelah Fix

### Halaman Gambaran Kelas
- ✅ Section "Kondisi Akademik" muncul
- ✅ Menampilkan rata-rata, tertinggi, terendah
- ✅ Menampilkan ranking 5 terbawah
- ✅ Jika belum ada data nilai, menampilkan pesan informatif

### Form Edit/Tambah Siswa
- ✅ Field "Nilai Rata-rata" tersedia
- ✅ Validasi input 0-100 dengan 1 desimal
- ✅ Field opsional (boleh kosong)

## Troubleshooting Lanjutan

### Jika Masih Error Setelah Menambah Kolom
1. **Clear browser cache** dan refresh halaman
2. **Restart Apache/MySQL** di XAMPP
3. **Cek error log** di `C:\xampp\apache\logs\error.log`

### Jika Kolom Sudah Ada Tapi Masih Error
1. Cek apakah ada typo di nama kolom:
   ```sql
   SHOW COLUMNS FROM siswa WHERE Field LIKE '%nilai%';
   ```
2. Pastikan tipe data benar:
   ```sql
   SELECT COLUMN_NAME, DATA_TYPE 
   FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE TABLE_NAME = 'siswa' AND COLUMN_NAME = 'nilai_rata_rata';
   ```

### Jika Ingin Menghapus Fitur Nilai
Jika tidak ingin menggunakan fitur ini, jalankan:
```sql
ALTER TABLE siswa DROP COLUMN nilai_rata_rata;
```

## File yang Sudah Diperbaiki

Semua file berikut sudah diperbaiki untuk menangani kasus ketika kolom belum ada:

1. ✅ `guru/gambaran_kelas.php` - Menampilkan pesan instruksi jika kolom belum ada
2. ✅ `guru/edit_siswa.php` - Field nilai hanya muncul jika kolom ada
3. ✅ `admin/add_siswa.php` - Field nilai hanya muncul jika kolom ada

## Langkah Selanjutnya

Setelah fix berhasil:
1. **Test semua fitur** untuk memastikan tidak ada error
2. **Tambah data nilai** melalui form edit siswa
3. **Lihat hasil** di halaman gambaran kelas
4. **Gunakan utility** `add_sample_grades.php` untuk data contoh

## Kontak Support

Jika masih mengalami masalah:
1. Screenshot error yang muncul
2. Copy-paste query yang dijalankan
3. Cek versi PHP dan MySQL yang digunakan
