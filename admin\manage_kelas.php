<?php
// admin/manage_kelas.php

// Include header yang sudah menghandle autentikasi
include '../includes/header.php';

// Cek apakah user adalah admin
if ($_SESSION['role'] !== 'admin') {
    die('<div class="alert alert-danger">Aks<PERSON> ditolak. Halaman ini hanya untuk administrator.</div>');
}

$message = '';

// Handle form submissions
if ($_POST) {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add_kelas':
                $nama_kelas = trim($_POST['nama_kelas']);
                $tingkat = trim($_POST['tingkat']);
                
                if (empty($nama_kelas)) {
                    $message = '<div class="alert alert-danger">Nama kelas harus diisi.</div>';
                } else {
                    // Cek nama kelas sudah ada
                    $stmt = $conn->prepare("SELECT id_kelas FROM kelas WHERE nama_kelas = ?");
                    $stmt->bind_param("s", $nama_kelas);
                    $stmt->execute();
                    
                    if ($stmt->get_result()->num_rows > 0) {
                        $message = '<div class="alert alert-danger">Nama kelas sudah ada.</div>';
                    } else {
                        // Insert kelas
                        $stmt = $conn->prepare("INSERT INTO kelas (nama_kelas, tingkat) VALUES (?, ?)");
                        $stmt->bind_param("ss", $nama_kelas, $tingkat);
                        
                        if ($stmt->execute()) {
                            $message = '<div class="alert alert-success">Kelas berhasil ditambahkan.</div>';
                        } else {
                            $message = '<div class="alert alert-danger">Gagal menambahkan kelas.</div>';
                        }
                    }
                }
                break;
                
            case 'edit_kelas':
                $id_kelas = $_POST['id_kelas'];
                $nama_kelas = trim($_POST['nama_kelas']);
                $tingkat = trim($_POST['tingkat']);
                
                if (empty($nama_kelas)) {
                    $message = '<div class="alert alert-danger">Nama kelas harus diisi.</div>';
                } else {
                    // Cek nama kelas sudah ada (kecuali untuk kelas yang sedang diedit)
                    $stmt = $conn->prepare("SELECT id_kelas FROM kelas WHERE nama_kelas = ? AND id_kelas != ?");
                    $stmt->bind_param("si", $nama_kelas, $id_kelas);
                    $stmt->execute();
                    
                    if ($stmt->get_result()->num_rows > 0) {
                        $message = '<div class="alert alert-danger">Nama kelas sudah ada.</div>';
                    } else {
                        // Update kelas
                        $stmt = $conn->prepare("UPDATE kelas SET nama_kelas = ?, tingkat = ? WHERE id_kelas = ?");
                        $stmt->bind_param("ssi", $nama_kelas, $tingkat, $id_kelas);
                        
                        if ($stmt->execute()) {
                            $message = '<div class="alert alert-success">Kelas berhasil diperbarui.</div>';
                        } else {
                            $message = '<div class="alert alert-danger">Gagal memperbarui kelas.</div>';
                        }
                    }
                }
                break;
        }
    }
}

// Ambil data kelas dengan statistik
$stmt = $conn->prepare("
    SELECT k.*, 
           COUNT(DISTINCT s.id_siswa) as total_siswa,
           COUNT(DISTINCT uk.id_user) as total_wali,
           tp.tahun_ajaran
    FROM kelas k
    LEFT JOIN siswa s ON k.id_kelas = s.id_kelas
    LEFT JOIN user_kelas uk ON k.id_kelas = uk.id_kelas
    LEFT JOIN tahun_pelajaran tp ON uk.id_tahun = tp.id_tahun AND tp.is_active = 1
    GROUP BY k.id_kelas
    ORDER BY k.nama_kelas
");
$stmt->execute();
$kelas_list = $stmt->get_result();
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h3><i class="fas fa-school"></i> Kelola Kelas</h3>
    <div>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addKelasModal">
            <i class="fas fa-plus"></i> Tambah Kelas
        </button>
        <a href="dashboard.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>
</div>

<?= $message ?>

<!-- Tabel Kelas -->
<div class="card">
    <div class="card-header">
        <h5><i class="fas fa-list"></i> Daftar Kelas</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Nama Kelas</th>
                        <th>Tingkat</th>
                        <th>Total Siswa</th>
                        <th>Wali Kelas (Tahun Aktif)</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                    <?php while($kelas = $kelas_list->fetch_assoc()): ?>
                        <tr>
                            <td><strong><?= htmlspecialchars($kelas['nama_kelas']) ?></strong></td>
                            <td><?= htmlspecialchars($kelas['tingkat'] ?? '-') ?></td>
                            <td>
                                <span class="badge bg-info"><?= $kelas['total_siswa'] ?> siswa</span>
                            </td>
                            <td>
                                <?php if ($kelas['total_wali'] > 0): ?>
                                    <span class="badge bg-success"><?= $kelas['total_wali'] ?> wali kelas</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Belum ada wali</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <button class="btn btn-sm btn-warning" 
                                        onclick="editKelas(<?= $kelas['id_kelas'] ?>, '<?= htmlspecialchars($kelas['nama_kelas']) ?>', '<?= htmlspecialchars($kelas['tingkat'] ?? '') ?>')">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                
                                <a href="../guru/gambaran_kelas.php?kelas=<?= $kelas['id_kelas'] ?>&tahun=<?= getActiveTahun($conn)['id_tahun'] ?>" 
                                   class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> Lihat Data
                                </a>
                            </td>
                        </tr>
                    <?php endwhile; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal Tambah Kelas -->
<div class="modal fade" id="addKelasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tambah Kelas Baru</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_kelas">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Kelas *</label>
                        <input type="text" name="nama_kelas" class="form-control" 
                               placeholder="Contoh: KPP A, X-1, XI IPA" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Tingkat</label>
                        <input type="text" name="tingkat" class="form-control" 
                               placeholder="Contoh: Kelas Persiapan Pastoral, Kelas 10">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Tambah Kelas</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Edit Kelas -->
<div class="modal fade" id="editKelasModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Kelas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="edit_kelas">
                    <input type="hidden" name="id_kelas" id="edit_id_kelas">
                    
                    <div class="mb-3">
                        <label class="form-label">Nama Kelas *</label>
                        <input type="text" name="nama_kelas" id="edit_nama_kelas" class="form-control" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Tingkat</label>
                        <input type="text" name="tingkat" id="edit_tingkat" class="form-control">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editKelas(id, nama, tingkat) {
    document.getElementById('edit_id_kelas').value = id;
    document.getElementById('edit_nama_kelas').value = nama;
    document.getElementById('edit_tingkat').value = tingkat;
    
    var modal = new bootstrap.Modal(document.getElementById('editKelasModal'));
    modal.show();
}
</script>

<?php include '../includes/footer.php'; ?>
